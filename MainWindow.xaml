<Window x:Class="AirMonitor.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:sys="clr-namespace:System;assembly=mscorlib"
        mc:Ignorable="d"
        Title="{Binding FullTitle}"
        Height="600" Width="800"
        MinHeight="400" MinWidth="600"
        WindowStartupLocation="CenterScreen"
        Style="{StaticResource MainWindowStyle}">
    
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 菜单栏 -->
        <Menu Grid.Row="0" Style="{StaticResource MainMenuStyle}">
            <MenuItem Header="文件(_F)">
                <MenuItem Header="刷新(_R)" Command="{Binding RefreshCommand}"/>
                <MenuItem Header="保存设置(_S)" Command="{Binding SaveSettingsCommand}"/>
                <Separator/>
                <MenuItem Header="退出(_X)" Command="{Binding ExitApplicationCommand}"/>
            </MenuItem>
            <MenuItem Header="帮助(_H)">
                <MenuItem Header="关于(_A)" Command="{Binding ShowAboutCommand}"/>
            </MenuItem>
        </Menu>

        <!-- 主内容区域 -->
        <Grid Grid.Row="1" Margin="10">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>

            <!-- 工具栏 -->
            <ToolBar Grid.Row="0" Style="{StaticResource MainToolBarStyle}">
                <Button Content="刷新" Command="{Binding RefreshCommand}" Style="{StaticResource ToolBarButtonStyle}"/>
                <Button Content="保存设置" Command="{Binding SaveSettingsCommand}" Style="{StaticResource ToolBarButtonStyle}"/>
                <Separator/>
                <TextBlock Text="用户:" VerticalAlignment="Center" Margin="5,0"/>
                <TextBlock Text="{Binding CurrentUser}" VerticalAlignment="Center" FontWeight="Bold"/>
            </ToolBar>

            <!-- 主要内容 -->
            <Border Grid.Row="1" Style="{StaticResource ContentBorderStyle}" Margin="0,10,0,0">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <!-- 信息面板 -->
                    <StackPanel Grid.Row="0" Orientation="Vertical" Margin="10">
                        <TextBlock Text="应用程序信息" Style="{StaticResource HeaderTextStyle}"/>
                        <Grid Margin="0,10,0,0">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>

                            <TextBlock Grid.Row="0" Grid.Column="0" Text="应用程序:" Style="{StaticResource LabelTextStyle}"/>
                            <TextBlock Grid.Row="0" Grid.Column="1" Text="{Binding Title}" Style="{StaticResource ValueTextStyle}"/>

                            <TextBlock Grid.Row="1" Grid.Column="0" Text="版本:" Style="{StaticResource LabelTextStyle}"/>
                            <TextBlock Grid.Row="1" Grid.Column="1" Text="{Binding Version}" Style="{StaticResource ValueTextStyle}"/>

                            <TextBlock Grid.Row="2" Grid.Column="0" Text="环境:" Style="{StaticResource LabelTextStyle}"/>
                            <TextBlock Grid.Row="2" Grid.Column="1" Text="{Binding Environment}" Style="{StaticResource ValueTextStyle}"/>

                            <TextBlock Grid.Row="3" Grid.Column="0" Text="当前用户:" Style="{StaticResource LabelTextStyle}"/>
                            <TextBlock Grid.Row="3" Grid.Column="1" Text="{Binding CurrentUser}" Style="{StaticResource ValueTextStyle}"/>
                        </Grid>
                    </StackPanel>

                    <!-- 主要工作区域 -->
                    <Border Grid.Row="1" Style="{StaticResource WorkAreaBorderStyle}" Margin="10">
                        <Grid>
                            <TextBlock Text="主要工作区域" 
                                     HorizontalAlignment="Center" 
                                     VerticalAlignment="Center"
                                     Style="{StaticResource PlaceholderTextStyle}"/>
                            
                            <!-- 加载指示器 -->
                            <Grid Visibility="{Binding IsLoading, Converter={StaticResource BooleanToVisibilityConverter}}">
                                <Rectangle Fill="Black" Opacity="0.3"/>
                                <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                                    <ProgressBar IsIndeterminate="True" Width="200" Height="20"/>
                                    <TextBlock Text="正在加载..." HorizontalAlignment="Center" Margin="0,10,0,0" 
                                             Foreground="White" FontWeight="Bold"/>
                                </StackPanel>
                            </Grid>
                        </Grid>
                    </Border>
                </Grid>
            </Border>
        </Grid>

        <!-- 状态栏 -->
        <StatusBar Grid.Row="2" Style="{StaticResource MainStatusBarStyle}">
            <StatusBarItem>
                <TextBlock Text="{Binding StatusMessage}"/>
            </StatusBarItem>
            <StatusBarItem HorizontalAlignment="Right">
                <TextBlock Text="{Binding Source={x:Static sys:DateTime.Now}, StringFormat='yyyy-MM-dd HH:mm:ss'}" 
                           x:Name="TimeDisplay"/>
            </StatusBarItem>
        </StatusBar>
    </Grid>
</Window>
