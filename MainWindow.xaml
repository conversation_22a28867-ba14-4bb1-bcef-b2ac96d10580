<Window x:Class="AirMonitor.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:sys="clr-namespace:System;assembly=mscorlib"
        mc:Ignorable="d"
        Title="{Binding FullTitle}"
        Height="600" Width="800"
        MinHeight="400" MinWidth="600"
        WindowStartupLocation="CenterScreen"
        Background="{DynamicResource ApplicationPageBackgroundBrush}">
    
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 菜单栏 -->
        <Menu Grid.Row="0"
              Background="{DynamicResource ControlFillColorDefaultBrush}"
              Foreground="{DynamicResource TextFillColorPrimaryBrush}">
            <MenuItem Header="文件(_F)">
                <MenuItem Header="刷新(_R)" Command="{Binding RefreshCommand}">
                    <MenuItem.Icon>
                        <TextBlock Text="{StaticResource RefreshIcon}"
                                   Style="{StaticResource SmallIconStyle}"
                                   Foreground="{DynamicResource TextFillColorPrimaryBrush}"/>
                    </MenuItem.Icon>
                </MenuItem>
                <MenuItem Header="保存设置(_S)" Command="{Binding SaveSettingsCommand}">
                    <MenuItem.Icon>
                        <TextBlock Text="{StaticResource SaveIcon}"
                                   Style="{StaticResource SmallIconStyle}"
                                   Foreground="{DynamicResource TextFillColorPrimaryBrush}"/>
                    </MenuItem.Icon>
                </MenuItem>
                <Separator/>
                <MenuItem Header="退出(_X)" Command="{Binding ExitApplicationCommand}"/>
            </MenuItem>
            <MenuItem Header="视图(_V)">
                <MenuItem Header="切换主题(_T)" Click="ToggleTheme_Click">
                    <MenuItem.Icon>
                        <TextBlock Text="🌓"
                                   Style="{StaticResource SmallIconStyle}"
                                   Foreground="{DynamicResource TextFillColorPrimaryBrush}"/>
                    </MenuItem.Icon>
                </MenuItem>
                <MenuItem Header="Fluent设计演示(_D)" Click="ShowFluentDemo_Click"/>
            </MenuItem>
            <MenuItem Header="帮助(_H)">
                <MenuItem Header="关于(_A)" Command="{Binding ShowAboutCommand}">
                    <MenuItem.Icon>
                        <TextBlock Text="{StaticResource InfoIcon}"
                                   Style="{StaticResource SmallIconStyle}"
                                   Foreground="{DynamicResource TextFillColorPrimaryBrush}"/>
                    </MenuItem.Icon>
                </MenuItem>
            </MenuItem>
        </Menu>

        <!-- 主内容区域 -->
        <Grid Grid.Row="1" Margin="10">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>

            <!-- 工具栏 -->
            <ToolBar Grid.Row="0"
                     Background="{DynamicResource ControlFillColorSecondaryBrush}"
                     BorderBrush="{DynamicResource ControlStrokeColorDefaultBrush}"
                     BorderThickness="0,0,0,1">
                <Button Command="{Binding RefreshCommand}"
                        Style="{StaticResource IconButtonStyle}"
                        ToolTip="刷新">
                    <TextBlock Text="{StaticResource RefreshIcon}"
                               Style="{StaticResource MediumIconStyle}"
                               Foreground="{DynamicResource TextFillColorPrimaryBrush}"/>
                </Button>
                <Button Command="{Binding SaveSettingsCommand}"
                        Style="{StaticResource IconButtonStyle}"
                        ToolTip="保存设置">
                    <TextBlock Text="{StaticResource SaveIcon}"
                               Style="{StaticResource MediumIconStyle}"
                               Foreground="{DynamicResource TextFillColorPrimaryBrush}"/>
                </Button>
                <Separator/>
                <TextBlock Text="用户:"
                           VerticalAlignment="Center"
                           Margin="10,0,5,0"
                           Style="{StaticResource BodyTextStyle}"
                           Foreground="{DynamicResource TextFillColorSecondaryBrush}"/>
                <TextBlock Text="{Binding CurrentUser}"
                           VerticalAlignment="Center"
                           Style="{StaticResource BodyStrongTextStyle}"
                           Foreground="{DynamicResource TextFillColorPrimaryBrush}"/>
            </ToolBar>

            <!-- 主要内容 -->
            <Border Grid.Row="1"
                    Background="{DynamicResource CardBackgroundFillColorDefaultBrush}"
                    BorderBrush="{DynamicResource CardStrokeColorDefaultBrush}"
                    BorderThickness="1"
                    CornerRadius="8"
                    Margin="0,10,0,0">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <!-- 信息面板 -->
                    <Border Grid.Row="0"
                            Background="{DynamicResource ControlFillColorSecondaryBrush}"
                            CornerRadius="8,8,0,0"
                            Padding="20">
                        <StackPanel Orientation="Vertical">
                            <TextBlock Text="应用程序信息"
                                       Style="{StaticResource SubtitleTextStyle}"
                                       Foreground="{DynamicResource TextFillColorPrimaryBrush}"
                                       Margin="0,0,0,15"/>
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                </Grid.RowDefinitions>

                                <TextBlock Grid.Row="0" Grid.Column="0"
                                           Text="应用程序:"
                                           Style="{StaticResource BodyTextStyle}"
                                           Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                                           Margin="0,0,15,8"/>
                                <TextBlock Grid.Row="0" Grid.Column="1"
                                           Text="{Binding Title}"
                                           Style="{StaticResource BodyStrongTextStyle}"
                                           Foreground="{DynamicResource TextFillColorPrimaryBrush}"
                                           Margin="0,0,0,8"/>

                                <TextBlock Grid.Row="1" Grid.Column="0"
                                           Text="版本:"
                                           Style="{StaticResource BodyTextStyle}"
                                           Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                                           Margin="0,0,15,8"/>
                                <TextBlock Grid.Row="1" Grid.Column="1"
                                           Text="{Binding Version}"
                                           Style="{StaticResource BodyStrongTextStyle}"
                                           Foreground="{DynamicResource TextFillColorPrimaryBrush}"
                                           Margin="0,0,0,8"/>

                                <TextBlock Grid.Row="2" Grid.Column="0"
                                           Text="环境:"
                                           Style="{StaticResource BodyTextStyle}"
                                           Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                                           Margin="0,0,15,8"/>
                                <TextBlock Grid.Row="2" Grid.Column="1"
                                           Text="{Binding Environment}"
                                           Style="{StaticResource BodyStrongTextStyle}"
                                           Foreground="{DynamicResource TextFillColorPrimaryBrush}"
                                           Margin="0,0,0,8"/>

                                <TextBlock Grid.Row="3" Grid.Column="0"
                                           Text="当前用户:"
                                           Style="{StaticResource BodyTextStyle}"
                                           Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                                           Margin="0,0,15,0"/>
                                <TextBlock Grid.Row="3" Grid.Column="1"
                                           Text="{Binding CurrentUser}"
                                           Style="{StaticResource BodyStrongTextStyle}"
                                           Foreground="{DynamicResource TextFillColorPrimaryBrush}"/>
                            </Grid>
                        </StackPanel>
                    </Border>

                    <!-- 主要工作区域 -->
                    <Border Grid.Row="1"
                            Background="{DynamicResource ApplicationPageBackgroundBrush}"
                            CornerRadius="0,0,8,8"
                            Margin="20">
                        <Grid>
                            <StackPanel HorizontalAlignment="Center"
                                        VerticalAlignment="Center"
                                        Spacing="20">
                                <TextBlock Text="{StaticResource HomeIcon}"
                                           Style="{StaticResource ExtraLargeIconStyle}"
                                           Foreground="{DynamicResource TextFillColorTertiaryBrush}"
                                           HorizontalAlignment="Center"/>
                                <TextBlock Text="主要工作区域"
                                           Style="{StaticResource BodyLargeTextStyle}"
                                           Foreground="{DynamicResource TextFillColorTertiaryBrush}"
                                           HorizontalAlignment="Center"/>
                                <Button Content="查看Fluent设计演示"
                                        Click="ShowFluentDemo_Click"
                                        Background="{DynamicResource AccentFillColorDefaultBrush}"
                                        Foreground="White"
                                        BorderThickness="0"
                                        Padding="20,10"
                                        CornerRadius="4"/>
                            </StackPanel>

                            <!-- 加载指示器 -->
                            <Grid Visibility="{Binding IsLoading, Converter={StaticResource BooleanToVisibilityConverter}}">
                                <Rectangle Fill="{DynamicResource SmokeFillColorDefaultBrush}"/>
                                <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center" Spacing="15">
                                    <ProgressBar IsIndeterminate="True"
                                                 Width="200"
                                                 Height="4"
                                                 Background="{DynamicResource ControlFillColorSecondaryBrush}"
                                                 Foreground="{DynamicResource AccentFillColorDefaultBrush}"/>
                                    <TextBlock Text="正在加载..."
                                               HorizontalAlignment="Center"
                                               Style="{StaticResource BodyTextStyle}"
                                               Foreground="{DynamicResource TextFillColorInverseBrush}"/>
                                </StackPanel>
                            </Grid>
                        </Grid>
                    </Border>
                </Grid>
            </Border>
        </Grid>

        <!-- 状态栏 -->
        <StatusBar Grid.Row="2"
                   Background="{DynamicResource ControlFillColorDefaultBrush}"
                   BorderBrush="{DynamicResource ControlStrokeColorDefaultBrush}"
                   BorderThickness="0,1,0,0">
            <StatusBarItem>
                <TextBlock Text="{Binding StatusMessage}"
                           Style="{StaticResource CaptionTextStyle}"
                           Foreground="{DynamicResource TextFillColorSecondaryBrush}"/>
            </StatusBarItem>
            <StatusBarItem HorizontalAlignment="Right">
                <TextBlock Text="{Binding Source={x:Static sys:DateTime.Now}, StringFormat='yyyy-MM-dd HH:mm:ss'}"
                           x:Name="TimeDisplay"
                           Style="{StaticResource CaptionTextStyle}"
                           Foreground="{DynamicResource TextFillColorSecondaryBrush}"/>
            </StatusBarItem>
        </StatusBar>
    </Grid>
</Window>
