<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:sys="clr-namespace:System;assembly=mscorlib">

    <!-- 基础文本样式 -->
    <Style x:Key="BaseTextStyle" TargetType="TextBlock">
        <Setter Property="FontFamily" Value="{StaticResource PrimaryFontFamily}"/>
        <Setter Property="Foreground" Value="{StaticResource TextPrimaryBrush}"/>
        <Setter Property="TextWrapping" Value="Wrap"/>
    </Style>

    <!-- 标题文本样式 -->
    <Style x:Key="TitleTextStyle" TargetType="TextBlock" BasedOn="{StaticResource BaseTextStyle}">
        <Setter Property="FontSize" Value="{StaticResource FontSizeTitle}"/>
        <Setter Property="FontWeight" Value="{StaticResource FontWeightBold}"/>
        <Setter Property="Margin" Value="0,0,0,16"/>
    </Style>

    <!-- 标题文本样式 -->
    <Style x:Key="HeaderTextStyle" TargetType="TextBlock" BasedOn="{StaticResource BaseTextStyle}">
        <Setter Property="FontSize" Value="{StaticResource FontSizeHeader}"/>
        <Setter Property="FontWeight" Value="{StaticResource FontWeightSemiBold}"/>
        <Setter Property="Margin" Value="0,0,0,8"/>
    </Style>

    <!-- 正文文本样式 -->
    <Style x:Key="BodyTextStyle" TargetType="TextBlock" BasedOn="{StaticResource BaseTextStyle}">
        <Setter Property="FontSize" Value="{StaticResource FontSizeNormal}"/>
        <Setter Property="LineHeight" Value="20"/>
        <Setter Property="Margin" Value="0,0,0,8"/>
    </Style>

    <!-- 标签文本样式 -->
    <Style x:Key="LabelTextStyle" TargetType="TextBlock" BasedOn="{StaticResource BaseTextStyle}">
        <Setter Property="FontSize" Value="{StaticResource FontSizeNormal}"/>
        <Setter Property="FontWeight" Value="{StaticResource FontWeightMedium}"/>
        <Setter Property="Foreground" Value="{StaticResource TextSecondaryBrush}"/>
        <Setter Property="Margin" Value="0,0,8,4"/>
        <Setter Property="VerticalAlignment" Value="Center"/>
    </Style>

    <!-- 值文本样式 -->
    <Style x:Key="ValueTextStyle" TargetType="TextBlock" BasedOn="{StaticResource BaseTextStyle}">
        <Setter Property="FontSize" Value="{StaticResource FontSizeNormal}"/>
        <Setter Property="Margin" Value="0,0,0,4"/>
        <Setter Property="VerticalAlignment" Value="Center"/>
    </Style>

    <!-- 提示文本样式 -->
    <Style x:Key="HintTextStyle" TargetType="TextBlock" BasedOn="{StaticResource BaseTextStyle}">
        <Setter Property="FontSize" Value="{StaticResource FontSizeSmall}"/>
        <Setter Property="Foreground" Value="{StaticResource TextHintBrush}"/>
        <Setter Property="FontStyle" Value="Italic"/>
    </Style>

    <!-- 占位符文本样式 -->
    <Style x:Key="PlaceholderTextStyle" TargetType="TextBlock" BasedOn="{StaticResource BaseTextStyle}">
        <Setter Property="FontSize" Value="{StaticResource FontSizeLarge}"/>
        <Setter Property="Foreground" Value="{StaticResource TextHintBrush}"/>
        <Setter Property="FontWeight" Value="{StaticResource FontWeightLight}"/>
        <Setter Property="HorizontalAlignment" Value="Center"/>
        <Setter Property="VerticalAlignment" Value="Center"/>
    </Style>

    <!-- 错误文本样式 -->
    <Style x:Key="ErrorTextStyle" TargetType="TextBlock" BasedOn="{StaticResource BaseTextStyle}">
        <Setter Property="FontSize" Value="{StaticResource FontSizeSmall}"/>
        <Setter Property="Foreground" Value="{StaticResource ErrorBrush}"/>
        <Setter Property="FontWeight" Value="{StaticResource FontWeightMedium}"/>
    </Style>

    <!-- 成功文本样式 -->
    <Style x:Key="SuccessTextStyle" TargetType="TextBlock" BasedOn="{StaticResource BaseTextStyle}">
        <Setter Property="FontSize" Value="{StaticResource FontSizeSmall}"/>
        <Setter Property="Foreground" Value="{StaticResource SuccessBrush}"/>
        <Setter Property="FontWeight" Value="{StaticResource FontWeightMedium}"/>
    </Style>

    <!-- 警告文本样式 -->
    <Style x:Key="WarningTextStyle" TargetType="TextBlock" BasedOn="{StaticResource BaseTextStyle}">
        <Setter Property="FontSize" Value="{StaticResource FontSizeSmall}"/>
        <Setter Property="Foreground" Value="{StaticResource WarningBrush}"/>
        <Setter Property="FontWeight" Value="{StaticResource FontWeightMedium}"/>
    </Style>

    <!-- 窗口和控件样式 -->
    <Style x:Key="MainWindowStyle" TargetType="Window">
        <Setter Property="Background" Value="{StaticResource BackgroundBrush}"/>
        <Setter Property="FontFamily" Value="{StaticResource PrimaryFontFamily}"/>
        <Setter Property="FontSize" Value="{StaticResource FontSizeNormal}"/>
    </Style>

    <Style x:Key="MainMenuStyle" TargetType="Menu">
        <Setter Property="Background" Value="{StaticResource SurfaceBrush}"/>
        <Setter Property="BorderBrush" Value="{StaticResource BorderBrush}"/>
        <Setter Property="BorderThickness" Value="0,0,0,1"/>
    </Style>

    <Style x:Key="MainToolBarStyle" TargetType="ToolBar">
        <Setter Property="Background" Value="{StaticResource SurfaceBrush}"/>
        <Setter Property="BorderBrush" Value="{StaticResource BorderBrush}"/>
        <Setter Property="BorderThickness" Value="0,0,0,1"/>
    </Style>

    <Style x:Key="ContentBorderStyle" TargetType="Border">
        <Setter Property="Background" Value="{StaticResource SurfaceBrush}"/>
        <Setter Property="BorderBrush" Value="{StaticResource BorderBrush}"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="CornerRadius" Value="4"/>
    </Style>

    <Style x:Key="WorkAreaBorderStyle" TargetType="Border">
        <Setter Property="Background" Value="{StaticResource CardBrush}"/>
        <Setter Property="BorderBrush" Value="{StaticResource DividerBrush}"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="CornerRadius" Value="4"/>
    </Style>

    <Style x:Key="MainStatusBarStyle" TargetType="StatusBar">
        <Setter Property="Background" Value="{StaticResource SurfaceBrush}"/>
        <Setter Property="BorderBrush" Value="{StaticResource BorderBrush}"/>
        <Setter Property="BorderThickness" Value="0,1,0,0"/>
    </Style>

</ResourceDictionary>
