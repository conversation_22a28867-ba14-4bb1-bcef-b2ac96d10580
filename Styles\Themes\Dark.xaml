<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">

    <!-- ========================================
         Fluent Design System - 暗色主题
         Dark Theme Color Definitions
         ======================================== -->

    <!-- 应用程序背景色 -->
    <Color x:Key="ApplicationPageBackgroundThemeColor">#202020</Color>
    <SolidColorBrush x:Key="ApplicationPageBackgroundThemeBrush" Color="{StaticResource ApplicationPageBackgroundThemeColor}"/>

    <!-- 系统背景色 -->
    <Color x:Key="SystemBackgroundColor">#1C1C1C</Color>
    <Color x:Key="SystemChromeLowColor">#171717</Color>
    <Color x:Key="SystemChromeMediumLowColor">#2C2C2C</Color>
    <Color x:Key="SystemChromeMediumColor">#383838</Color>
    <Color x:Key="SystemChromeHighColor">#767676</Color>
    <Color x:Key="SystemChromeAltLowColor">#171717</Color>
    <Color x:Key="SystemChromeAltMediumLowColor">#2C2C2C</Color>
    <Color x:Key="SystemChromeAltMediumColor">#383838</Color>
    <Color x:Key="SystemChromeAltHighColor">#767676</Color>

    <!-- 系统文本色 -->
    <Color x:Key="SystemBaseHighColor">#FFFFFF</Color>
    <Color x:Key="SystemBaseMediumHighColor">#E4E4E4</Color>
    <Color x:Key="SystemBaseMediumColor">#C8C8C8</Color>
    <Color x:Key="SystemBaseMediumLowColor">#8B8B8B</Color>
    <Color x:Key="SystemBaseLowColor">#4F4F4F</Color>
    <Color x:Key="SystemAltHighColor">#000000</Color>
    <Color x:Key="SystemAltMediumHighColor">#0F0F0F</Color>
    <Color x:Key="SystemAltMediumColor">#1A1A1A</Color>
    <Color x:Key="SystemAltMediumLowColor">#262626</Color>
    <Color x:Key="SystemAltLowColor">#2F2F2F</Color>

    <!-- 控件背景色 -->
    <Color x:Key="ControlFillColorDefault">#B3FFFFFF</Color>
    <Color x:Key="ControlFillColorSecondary">#80FFFFFF</Color>
    <Color x:Key="ControlFillColorTertiary">#4DFFFFFF</Color>
    <Color x:Key="ControlFillColorDisabled">#4DFFFFFF</Color>
    <Color x:Key="ControlFillColorTransparent">#00FFFFFF</Color>
    <Color x:Key="ControlFillColorInputActive">#1E1E1E</Color>

    <!-- 控件边框色 -->
    <Color x:Key="ControlStrokeColorDefault">#12FFFFFF</Color>
    <Color x:Key="ControlStrokeColorSecondary">#18FFFFFF</Color>
    <Color x:Key="ControlStrokeColorOnAccentDefault">#14000000</Color>
    <Color x:Key="ControlStrokeColorOnAccentSecondary">#66000000</Color>
    <Color x:Key="ControlStrokeColorOnAccentTertiary">#37000000</Color>
    <Color x:Key="ControlStrokeColorOnAccentDisabled">#0FFFFFFF</Color>

    <!-- 控件文本色 -->
    <Color x:Key="TextFillColorPrimary">#FFFFFF</Color>
    <Color x:Key="TextFillColorSecondary">#C5FFFFFF</Color>
    <Color x:Key="TextFillColorTertiary">#87FFFFFF</Color>
    <Color x:Key="TextFillColorDisabled">#5DFFFFFF</Color>
    <Color x:Key="TextFillColorInverse">#E4000000</Color>

    <!-- 强调色相关 -->
    <Color x:Key="AccentFillColorDefault">{StaticResource SystemAccentColorLight2}</Color>
    <Color x:Key="AccentFillColorSecondary">{StaticResource SystemAccentColorLight1}</Color>
    <Color x:Key="AccentFillColorTertiary">{StaticResource SystemAccentColor}</Color>
    <Color x:Key="AccentFillColorDisabled">#28FFFFFF</Color>

    <!-- 卡片和表面色 -->
    <Color x:Key="CardBackgroundFillColorDefault">#0DFFFFFF</Color>
    <Color x:Key="CardBackgroundFillColorSecondary">#08FFFFFF</Color>
    <Color x:Key="CardStrokeColorDefault">#19000000</Color>
    <Color x:Key="CardStrokeColorDefaultSolid">#393939</Color>

    <!-- 烟雾和阴影色 -->
    <Color x:Key="SmokeFillColorDefault">#4D000000</Color>
    <Color x:Key="LayerFillColorDefault">#4C3A3A3A</Color>
    <Color x:Key="LayerFillColorAlt">#0D000000</Color>
    <Color x:Key="LayerOnAcrylicFillColorDefault">#09FFFFFF</Color>
    <Color x:Key="LayerOnAccentAcrylicFillColorDefault">#09FFFFFF</Color>

    <!-- 系统注意色 -->
    <Color x:Key="SystemAttentionColor">#60CDFF</Color>
    <Color x:Key="SystemNeutralColor">#8A8886</Color>
    <Color x:Key="SystemSolidAttentionColor">#0078D4</Color>

    <!-- 画刷资源 -->
    <SolidColorBrush x:Key="ApplicationPageBackgroundBrush" Color="{StaticResource ApplicationPageBackgroundThemeColor}"/>
    <SolidColorBrush x:Key="SystemBackgroundBrush" Color="{StaticResource SystemBackgroundColor}"/>
    
    <SolidColorBrush x:Key="SystemBaseHighBrush" Color="{StaticResource SystemBaseHighColor}"/>
    <SolidColorBrush x:Key="SystemBaseMediumHighBrush" Color="{StaticResource SystemBaseMediumHighColor}"/>
    <SolidColorBrush x:Key="SystemBaseMediumBrush" Color="{StaticResource SystemBaseMediumColor}"/>
    <SolidColorBrush x:Key="SystemBaseMediumLowBrush" Color="{StaticResource SystemBaseMediumLowColor}"/>
    <SolidColorBrush x:Key="SystemBaseLowBrush" Color="{StaticResource SystemBaseLowColor}"/>
    
    <SolidColorBrush x:Key="ControlFillColorDefaultBrush" Color="{StaticResource ControlFillColorDefault}"/>
    <SolidColorBrush x:Key="ControlFillColorSecondaryBrush" Color="{StaticResource ControlFillColorSecondary}"/>
    <SolidColorBrush x:Key="ControlFillColorTertiaryBrush" Color="{StaticResource ControlFillColorTertiary}"/>
    <SolidColorBrush x:Key="ControlFillColorDisabledBrush" Color="{StaticResource ControlFillColorDisabled}"/>
    <SolidColorBrush x:Key="ControlFillColorTransparentBrush" Color="{StaticResource ControlFillColorTransparent}"/>
    <SolidColorBrush x:Key="ControlFillColorInputActiveBrush" Color="{StaticResource ControlFillColorInputActive}"/>
    
    <SolidColorBrush x:Key="ControlStrokeColorDefaultBrush" Color="{StaticResource ControlStrokeColorDefault}"/>
    <SolidColorBrush x:Key="ControlStrokeColorSecondaryBrush" Color="{StaticResource ControlStrokeColorSecondary}"/>
    
    <SolidColorBrush x:Key="TextFillColorPrimaryBrush" Color="{StaticResource TextFillColorPrimary}"/>
    <SolidColorBrush x:Key="TextFillColorSecondaryBrush" Color="{StaticResource TextFillColorSecondary}"/>
    <SolidColorBrush x:Key="TextFillColorTertiaryBrush" Color="{StaticResource TextFillColorTertiary}"/>
    <SolidColorBrush x:Key="TextFillColorDisabledBrush" Color="{StaticResource TextFillColorDisabled}"/>
    <SolidColorBrush x:Key="TextFillColorInverseBrush" Color="{StaticResource TextFillColorInverse}"/>
    
    <SolidColorBrush x:Key="AccentFillColorDefaultBrush" Color="{StaticResource AccentFillColorDefault}"/>
    <SolidColorBrush x:Key="AccentFillColorSecondaryBrush" Color="{StaticResource AccentFillColorSecondary}"/>
    <SolidColorBrush x:Key="AccentFillColorTertiaryBrush" Color="{StaticResource AccentFillColorTertiary}"/>
    <SolidColorBrush x:Key="AccentFillColorDisabledBrush" Color="{StaticResource AccentFillColorDisabled}"/>
    
    <SolidColorBrush x:Key="CardBackgroundFillColorDefaultBrush" Color="{StaticResource CardBackgroundFillColorDefault}"/>
    <SolidColorBrush x:Key="CardBackgroundFillColorSecondaryBrush" Color="{StaticResource CardBackgroundFillColorSecondary}"/>
    <SolidColorBrush x:Key="CardStrokeColorDefaultBrush" Color="{StaticResource CardStrokeColorDefault}"/>
    <SolidColorBrush x:Key="CardStrokeColorDefaultSolidBrush" Color="{StaticResource CardStrokeColorDefaultSolid}"/>
    
    <SolidColorBrush x:Key="SmokeFillColorDefaultBrush" Color="{StaticResource SmokeFillColorDefault}"/>
    <SolidColorBrush x:Key="LayerFillColorDefaultBrush" Color="{StaticResource LayerFillColorDefault}"/>
    <SolidColorBrush x:Key="LayerFillColorAltBrush" Color="{StaticResource LayerFillColorAlt}"/>

    <!-- 语义色画刷 -->
    <SolidColorBrush x:Key="SuccessBrush" Color="{StaticResource SharedGreen}"/>
    <SolidColorBrush x:Key="WarningBrush" Color="{StaticResource SharedYellow}"/>
    <SolidColorBrush x:Key="ErrorBrush" Color="{StaticResource SharedRed}"/>
    <SolidColorBrush x:Key="InfoBrush" Color="{StaticResource SharedBlue}"/>

</ResourceDictionary>
