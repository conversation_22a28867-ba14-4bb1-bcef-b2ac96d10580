using System;
using System.Collections.Generic;
using System.Windows;
using Microsoft.Win32;
using Microsoft.Extensions.Logging;

namespace AirMonitor.Services;

/// <summary>
/// 主题服务实现
/// 负责管理应用程序的主题切换
/// </summary>
public class ThemeService : IThemeService
{
    private readonly ILogger<ThemeService> _logger;
    private AppTheme _currentTheme = AppTheme.Light;

    public ThemeService(ILogger<ThemeService> logger)
    {
        _logger = logger;
        
        // 监听系统主题变化
        SystemEvents.UserPreferenceChanged += OnSystemPreferenceChanged;
    }

    public AppTheme CurrentTheme => _currentTheme;

    public event EventHandler<ThemeChangedEventArgs>? ThemeChanged;

    public void SetTheme(AppTheme theme)
    {
        var oldTheme = _currentTheme;
        
        if (theme == AppTheme.System)
        {
            theme = GetSystemTheme();
        }

        if (_currentTheme == theme)
        {
            return;
        }

        _currentTheme = theme;
        ApplyTheme(theme);
        
        _logger.LogInformation("主题已切换: {OldTheme} -> {NewTheme}", oldTheme, theme);
        ThemeChanged?.Invoke(this, new ThemeChangedEventArgs(theme, oldTheme));
    }

    public void ToggleTheme()
    {
        var newTheme = _currentTheme == AppTheme.Light ? AppTheme.Dark : AppTheme.Light;
        SetTheme(newTheme);
    }

    public void SetSystemTheme()
    {
        SetTheme(AppTheme.System);
    }

    public AppTheme GetSystemTheme()
    {
        try
        {
            using var key = Registry.CurrentUser.OpenSubKey(@"Software\Microsoft\Windows\CurrentVersion\Themes\Personalize");
            var appsUseLightTheme = key?.GetValue("AppsUseLightTheme");
            
            if (appsUseLightTheme is int value)
            {
                return value == 1 ? AppTheme.Light : AppTheme.Dark;
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "无法获取系统主题设置，使用默认亮色主题");
        }

        return AppTheme.Light;
    }

    private void ApplyTheme(AppTheme theme)
    {
        try
        {
            var app = Application.Current;
            if (app?.Resources == null)
            {
                _logger.LogWarning("无法获取应用程序资源，主题切换失败");
                return;
            }

            // 移除现有主题资源
            RemoveThemeResources(app.Resources);

            // 添加新主题资源
            var themeUri = theme switch
            {
                AppTheme.Light => new Uri("pack://application:,,,/Styles/Themes/Light.xaml"),
                AppTheme.Dark => new Uri("pack://application:,,,/Styles/Themes/Dark.xaml"),
                _ => new Uri("pack://application:,,,/Styles/Themes/Light.xaml")
            };

            var themeDict = new ResourceDictionary { Source = themeUri };
            app.Resources.MergedDictionaries.Add(themeDict);

            _logger.LogDebug("主题资源已应用: {ThemeUri}", themeUri);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "应用主题时发生错误: {Theme}", theme);
        }
    }

    private void RemoveThemeResources(ResourceDictionary resources)
    {
        var toRemove = new List<ResourceDictionary>();
        
        foreach (var dict in resources.MergedDictionaries)
        {
            if (dict.Source != null && 
                (dict.Source.ToString().Contains("/Themes/Light.xaml") || 
                 dict.Source.ToString().Contains("/Themes/Dark.xaml")))
            {
                toRemove.Add(dict);
            }
        }

        foreach (var dict in toRemove)
        {
            resources.MergedDictionaries.Remove(dict);
        }
    }

    private void OnSystemPreferenceChanged(object sender, UserPreferenceChangedEventArgs e)
    {
        if (e.Category == UserPreferenceCategory.General)
        {
            // 如果当前设置为跟随系统，则更新主题
            if (_currentTheme == AppTheme.System)
            {
                Application.Current?.Dispatcher.BeginInvoke(() =>
                {
                    var systemTheme = GetSystemTheme();
                    ApplyTheme(systemTheme);
                    _logger.LogInformation("系统主题已变更，自动切换到: {Theme}", systemTheme);
                });
            }
        }
    }
}
