<Window x:Class="AirMonitor.Views.FluentDesignDemo"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="Fluent Design System 演示" 
        Height="800" 
        Width="1200"
        Background="{DynamicResource ApplicationPageBackgroundBrush}"
        WindowStartupLocation="CenterScreen">

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- 标题栏 -->
        <Grid Grid.Row="0" Margin="0,0,0,20">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>

            <TextBlock Grid.Column="0" 
                       Text="Fluent Design System 演示"
                       Style="{StaticResource TitleTextStyle}"
                       Foreground="{DynamicResource TextFillColorPrimaryBrush}"/>

            <StackPanel Grid.Column="1" Orientation="Horizontal" Spacing="10">
                <Button Content="切换主题" 
                        Click="ToggleTheme_Click"
                        Padding="16,8"
                        Background="{DynamicResource AccentFillColorDefaultBrush}"
                        Foreground="White"
                        BorderThickness="0"
                        CornerRadius="4"/>
                
                <Button Style="{StaticResource IconButtonStyle}"
                        ToolTip="设置">
                    <TextBlock Text="{StaticResource SettingsIcon}"
                               Style="{StaticResource MediumIconStyle}"
                               Foreground="{DynamicResource TextFillColorPrimaryBrush}"/>
                </Button>
            </StackPanel>
        </Grid>

        <!-- 主内容区域 -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
            <StackPanel Spacing="30">

                <!-- 色彩系统演示 -->
                <Border Background="{DynamicResource CardBackgroundFillColorDefaultBrush}"
                        BorderBrush="{DynamicResource CardStrokeColorDefaultBrush}"
                        BorderThickness="1"
                        CornerRadius="8"
                        Padding="20">
                    <StackPanel Spacing="15">
                        <TextBlock Text="色彩系统"
                                   Style="{StaticResource SubtitleTextStyle}"
                                   Foreground="{DynamicResource TextFillColorPrimaryBrush}"/>

                        <!-- 主色调 -->
                        <StackPanel Spacing="10">
                            <TextBlock Text="主色调 (Accent Colors)"
                                       Style="{StaticResource BodyStrongTextStyle}"
                                       Foreground="{DynamicResource TextFillColorPrimaryBrush}"/>
                            <StackPanel Orientation="Horizontal" Spacing="10">
                                <Rectangle Width="60" Height="40" Fill="{DynamicResource AccentFillColorDefaultBrush}" RadiusX="4" RadiusY="4"/>
                                <Rectangle Width="60" Height="40" Fill="{DynamicResource AccentFillColorSecondaryBrush}" RadiusX="4" RadiusY="4"/>
                                <Rectangle Width="60" Height="40" Fill="{DynamicResource AccentFillColorTertiaryBrush}" RadiusX="4" RadiusY="4"/>
                            </StackPanel>
                        </StackPanel>

                        <!-- 语义色彩 -->
                        <StackPanel Spacing="10">
                            <TextBlock Text="语义色彩 (Semantic Colors)"
                                       Style="{StaticResource BodyStrongTextStyle}"
                                       Foreground="{DynamicResource TextFillColorPrimaryBrush}"/>
                            <StackPanel Orientation="Horizontal" Spacing="10">
                                <Rectangle Width="60" Height="40" Fill="{DynamicResource SuccessBrush}" RadiusX="4" RadiusY="4"/>
                                <Rectangle Width="60" Height="40" Fill="{DynamicResource WarningBrush}" RadiusX="4" RadiusY="4"/>
                                <Rectangle Width="60" Height="40" Fill="{DynamicResource ErrorBrush}" RadiusX="4" RadiusY="4"/>
                                <Rectangle Width="60" Height="40" Fill="{DynamicResource InfoBrush}" RadiusX="4" RadiusY="4"/>
                            </StackPanel>
                        </StackPanel>
                    </StackPanel>
                </Border>

                <!-- 字体系统演示 -->
                <Border Background="{DynamicResource CardBackgroundFillColorDefaultBrush}"
                        BorderBrush="{DynamicResource CardStrokeColorDefaultBrush}"
                        BorderThickness="1"
                        CornerRadius="8"
                        Padding="20">
                    <StackPanel Spacing="15">
                        <TextBlock Text="字体系统"
                                   Style="{StaticResource SubtitleTextStyle}"
                                   Foreground="{DynamicResource TextFillColorPrimaryBrush}"/>

                        <TextBlock Text="Display - 68px"
                                   Style="{StaticResource DisplayTextStyle}"
                                   Foreground="{DynamicResource TextFillColorPrimaryBrush}"/>

                        <TextBlock Text="Title Large - 40px"
                                   Style="{StaticResource TitleLargeTextStyle}"
                                   Foreground="{DynamicResource TextFillColorPrimaryBrush}"/>

                        <TextBlock Text="Title - 28px"
                                   Style="{StaticResource TitleTextStyle}"
                                   Foreground="{DynamicResource TextFillColorPrimaryBrush}"/>

                        <TextBlock Text="Subtitle - 20px"
                                   Style="{StaticResource SubtitleTextStyle}"
                                   Foreground="{DynamicResource TextFillColorPrimaryBrush}"/>

                        <TextBlock Text="Body Large - 16px"
                                   Style="{StaticResource BodyLargeTextStyle}"
                                   Foreground="{DynamicResource TextFillColorPrimaryBrush}"/>

                        <TextBlock Text="Body - 14px"
                                   Style="{StaticResource BodyTextStyle}"
                                   Foreground="{DynamicResource TextFillColorPrimaryBrush}"/>

                        <TextBlock Text="Body Strong - 14px"
                                   Style="{StaticResource BodyStrongTextStyle}"
                                   Foreground="{DynamicResource TextFillColorPrimaryBrush}"/>

                        <TextBlock Text="Caption - 12px"
                                   Style="{StaticResource CaptionTextStyle}"
                                   Foreground="{DynamicResource TextFillColorSecondaryBrush}"/>
                    </StackPanel>
                </Border>

                <!-- 图标系统演示 -->
                <Border Background="{DynamicResource CardBackgroundFillColorDefaultBrush}"
                        BorderBrush="{DynamicResource CardStrokeColorDefaultBrush}"
                        BorderThickness="1"
                        CornerRadius="8"
                        Padding="20">
                    <StackPanel Spacing="15">
                        <TextBlock Text="图标系统"
                                   Style="{StaticResource SubtitleTextStyle}"
                                   Foreground="{DynamicResource TextFillColorPrimaryBrush}"/>

                        <StackPanel Orientation="Horizontal" Spacing="20">
                            <StackPanel Spacing="5" HorizontalAlignment="Center">
                                <TextBlock Text="{StaticResource HomeIcon}"
                                           Style="{StaticResource LargeIconStyle}"
                                           Foreground="{DynamicResource TextFillColorPrimaryBrush}"/>
                                <TextBlock Text="Home" 
                                           Style="{StaticResource CaptionTextStyle}"
                                           Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                                           HorizontalAlignment="Center"/>
                            </StackPanel>

                            <StackPanel Spacing="5" HorizontalAlignment="Center">
                                <TextBlock Text="{StaticResource SettingsIcon}"
                                           Style="{StaticResource LargeIconStyle}"
                                           Foreground="{DynamicResource TextFillColorPrimaryBrush}"/>
                                <TextBlock Text="Settings" 
                                           Style="{StaticResource CaptionTextStyle}"
                                           Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                                           HorizontalAlignment="Center"/>
                            </StackPanel>

                            <StackPanel Spacing="5" HorizontalAlignment="Center">
                                <TextBlock Text="{StaticResource SearchIcon}"
                                           Style="{StaticResource LargeIconStyle}"
                                           Foreground="{DynamicResource TextFillColorPrimaryBrush}"/>
                                <TextBlock Text="Search" 
                                           Style="{StaticResource CaptionTextStyle}"
                                           Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                                           HorizontalAlignment="Center"/>
                            </StackPanel>

                            <StackPanel Spacing="5" HorizontalAlignment="Center">
                                <TextBlock Text="{StaticResource InfoIcon}"
                                           Style="{StaticResource LargeIconStyle}"
                                           Foreground="{DynamicResource InfoBrush}"/>
                                <TextBlock Text="Info" 
                                           Style="{StaticResource CaptionTextStyle}"
                                           Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                                           HorizontalAlignment="Center"/>
                            </StackPanel>

                            <StackPanel Spacing="5" HorizontalAlignment="Center">
                                <TextBlock Text="{StaticResource WarningIcon}"
                                           Style="{StaticResource LargeIconStyle}"
                                           Foreground="{DynamicResource WarningBrush}"/>
                                <TextBlock Text="Warning" 
                                           Style="{StaticResource CaptionTextStyle}"
                                           Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                                           HorizontalAlignment="Center"/>
                            </StackPanel>

                            <StackPanel Spacing="5" HorizontalAlignment="Center">
                                <TextBlock Text="{StaticResource ErrorIcon}"
                                           Style="{StaticResource LargeIconStyle}"
                                           Foreground="{DynamicResource ErrorBrush}"/>
                                <TextBlock Text="Error" 
                                           Style="{StaticResource CaptionTextStyle}"
                                           Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                                           HorizontalAlignment="Center"/>
                            </StackPanel>
                        </StackPanel>
                    </StackPanel>
                </Border>

                <!-- 控件演示 -->
                <Border Background="{DynamicResource CardBackgroundFillColorDefaultBrush}"
                        BorderBrush="{DynamicResource CardStrokeColorDefaultBrush}"
                        BorderThickness="1"
                        CornerRadius="8"
                        Padding="20">
                    <StackPanel Spacing="15">
                        <TextBlock Text="控件演示"
                                   Style="{StaticResource SubtitleTextStyle}"
                                   Foreground="{DynamicResource TextFillColorPrimaryBrush}"/>

                        <StackPanel Orientation="Horizontal" Spacing="15">
                            <Button Content="主要按钮"
                                    Background="{DynamicResource AccentFillColorDefaultBrush}"
                                    Foreground="White"
                                    BorderThickness="0"
                                    Padding="16,8"
                                    CornerRadius="4"/>

                            <Button Content="次要按钮"
                                    Background="{DynamicResource ControlFillColorDefaultBrush}"
                                    Foreground="{DynamicResource TextFillColorPrimaryBrush}"
                                    BorderBrush="{DynamicResource ControlStrokeColorDefaultBrush}"
                                    BorderThickness="1"
                                    Padding="16,8"
                                    CornerRadius="4"/>

                            <Button Style="{StaticResource IconButtonStyle}">
                                <TextBlock Text="{StaticResource AddIcon}"
                                           Style="{StaticResource MediumIconStyle}"
                                           Foreground="{DynamicResource TextFillColorPrimaryBrush}"/>
                            </Button>
                        </StackPanel>

                        <TextBox Text="输入框示例"
                                 Background="{DynamicResource ControlFillColorInputActiveBrush}"
                                 Foreground="{DynamicResource TextFillColorPrimaryBrush}"
                                 BorderBrush="{DynamicResource ControlStrokeColorDefaultBrush}"
                                 BorderThickness="1"
                                 Padding="12,8"
                                 Width="200"
                                 HorizontalAlignment="Left"/>
                    </StackPanel>
                </Border>

            </StackPanel>
        </ScrollViewer>
    </Grid>
</Window>
