using AirMonitor.Services;
using AirMonitor.ViewModels;
using Microsoft.Extensions.Hosting;
using Serilog;
using System.Windows;

namespace AirMonitor;

/// <summary>
/// App.xaml 的交互逻辑
/// </summary>
public partial class App : Application
{
    private IHost? _host;

    protected override async void OnStartup(StartupEventArgs e)
    {
        try
        {
            // 配置Serilog
            var configuration = GetConfiguration();
            Log.Logger = new LoggerConfiguration()
                .WriteTo.Console()
                .WriteTo.File("logs/airmonitor-.log", rollingInterval: RollingInterval.Day)
                .CreateLogger();

            // 创建主机
            _host = CreateHostBuilder(e.Args).Build();

            // 启动服务
            await _host.StartAsync();

            // 初始化数据服务
            var dataService = _host.Services.GetRequiredService<IDataService>();
            await dataService.InitializeAsync();

            // 获取主窗口并设置数据上下文
            var mainWindow = _host.Services.GetRequiredService<MainWindow>();
            var mainViewModel = _host.Services.GetRequiredService<MainViewModel>();
            mainWindow.DataContext = mainViewModel;

            // 显示主窗口
            mainWindow.Show();

            Log.Information("应用程序启动完成");
        }
        catch (Exception ex)
        {
            Log.Fatal(ex, "应用程序启动失败");
            MessageBox.Show($"应用程序启动失败：{ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            Current.Shutdown(1);
        }

        base.OnStartup(e);
    }

    protected override async void OnExit(ExitEventArgs e)
    {
        try
        {
            if (_host != null)
            {
                await _host.StopAsync();
                _host.Dispose();
            }

            Log.Information("应用程序正常退出");
        }
        catch (Exception ex)
        {
            Log.Error(ex, "应用程序退出时发生错误");
        }
        finally
        {
            Log.CloseAndFlush();
        }

        base.OnExit(e);
    }

    private static IHostBuilder CreateHostBuilder(string[] args) =>
        Host.CreateDefaultBuilder(args)
            .UseSerilog()
            .ConfigureServices((context, services) =>
            {
                // 注册配置服务
                services.AddSingleton<IConfigurationService, ConfigurationService>();

                // 注册数据服务
                services.AddSingleton<IDataService, DataService>();

                // 注册ViewModels
                services.AddTransient<MainViewModel>();

                // 注册Views
                services.AddTransient<MainWindow>();

                // 注册消息服务
                services.AddSingleton<IMessenger>(WeakReferenceMessenger.Default);
            });

    private static IConfiguration GetConfiguration()
    {
        return new ConfigurationBuilder()
            .SetBasePath(AppDomain.CurrentDomain.BaseDirectory)
            .AddJsonFile("appsettings.json", optional: false, reloadOnChange: true)
            .AddJsonFile("user-settings.json", optional: true, reloadOnChange: true)
            .Build();
    }
}
