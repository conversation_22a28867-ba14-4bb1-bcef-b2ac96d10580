# Fluent Design System for WPF

这是一套完整的基于Microsoft Fluent Design System的WPF设计系统实现，提供了现代化的UI组件和主题支持。

## 📁 文件结构

```
Styles/
├── Colors.xaml          # 基础色彩定义
├── Fonts.xaml           # 字体系统定义
├── Icons.xaml           # 图标资源定义
├── ButtonStyles.xaml    # 按钮样式
├── TextStyles.xaml      # 文本样式
├── Themes/
│   ├── Light.xaml       # 亮色主题
│   └── Dark.xaml        # 暗色主题
└── README.md           # 本文档
```

## 🎨 色彩系统

### 主色调 (Accent Colors)
- `SystemAccentColor` - 主强调色 (#0078D4)
- `SystemAccentColorLight1-3` - 亮色变体
- `SystemAccentColorDark1-3` - 暗色变体

### 中性色调 (Gray Scale)
- `Gray10` 到 `Gray200` - 完整的灰度色阶
- 从最亮的 #FAF9F8 到最暗的 #11100F

### 语义色彩 (Semantic Colors)
- `SharedGreen` - 成功色 (#107C10)
- `SharedRed` - 错误色 (#D13438)
- `SharedYellow` - 警告色 (#FFB900)
- `SharedBlue` - 信息色 (#0078D4)

### 透明度变体
- `SystemAccentColorAlpha10-90` - 强调色透明度变体
- `BlackAlpha10-90` - 黑色透明度变体
- `WhiteAlpha10-90` - 白色透明度变体

## 🔤 字体系统

### 字体家族
- `PrimaryFontFamily` - 主字体 (Segoe UI, Microsoft YaHei UI, Arial)
- `MonospaceFontFamily` - 等宽字体 (Cascadia Code, Consolas)
- `IconFontFamily` - 图标字体 (Segoe MDL2 Assets)

### 字体层次
- **Display** - 68px, 用于大标题
- **Title Large** - 40px, 用于页面标题
- **Title** - 28px, 用于区块标题
- **Subtitle** - 20px, 用于子标题
- **Body Large** - 16px, 用于重要正文
- **Body** - 14px, 用于普通正文
- **Caption** - 12px, 用于说明文字

### 使用示例
```xml
<TextBlock Text="这是标题" Style="{StaticResource TitleTextStyle}"/>
<TextBlock Text="这是正文" Style="{StaticResource BodyTextStyle}"/>
<TextBlock Text="这是说明" Style="{StaticResource CaptionTextStyle}"/>
```

## 🎯 图标系统

### 常用图标
- 导航: `HomeIcon`, `BackIcon`, `ForwardIcon`, `RefreshIcon`
- 操作: `AddIcon`, `DeleteIcon`, `EditIcon`, `SaveIcon`
- 文件: `FolderIcon`, `FileIcon`, `OpenIcon`
- 状态: `InfoIcon`, `WarningIcon`, `ErrorIcon`, `SuccessIcon`

### 图标样式
- `SmallIconStyle` - 12px
- `MediumIconStyle` - 16px
- `LargeIconStyle` - 20px
- `ExtraLargeIconStyle` - 24px

### 使用示例
```xml
<TextBlock Text="{StaticResource HomeIcon}" 
           Style="{StaticResource MediumIconStyle}"/>

<Button Style="{StaticResource IconButtonStyle}">
    <TextBlock Text="{StaticResource SettingsIcon}" 
               Style="{StaticResource MediumIconStyle}"/>
</Button>
```

## 🌓 主题系统

### 支持的主题
- **Light** - 亮色主题
- **Dark** - 暗色主题
- **System** - 跟随系统主题

### 主题服务使用
```csharp
// 注册服务
services.AddSingleton<IThemeService, ThemeService>();

// 使用服务
public class MainViewModel
{
    private readonly IThemeService _themeService;
    
    public MainViewModel(IThemeService themeService)
    {
        _themeService = themeService;
    }
    
    [RelayCommand]
    private void ToggleTheme()
    {
        _themeService.ToggleTheme();
    }
}
```

### 动态资源绑定
所有颜色资源都支持动态绑定，主题切换时会自动更新：

```xml
<Border Background="{DynamicResource CardBackgroundFillColorDefaultBrush}"
        BorderBrush="{DynamicResource CardStrokeColorDefaultBrush}">
    <TextBlock Foreground="{DynamicResource TextFillColorPrimaryBrush}"/>
</Border>
```

## 🎛️ 控件样式

### 按钮样式
```xml
<!-- 主要按钮 -->
<Button Content="主要按钮"
        Background="{DynamicResource AccentFillColorDefaultBrush}"
        Foreground="White"/>

<!-- 次要按钮 -->
<Button Content="次要按钮"
        Background="{DynamicResource ControlFillColorDefaultBrush}"
        BorderBrush="{DynamicResource ControlStrokeColorDefaultBrush}"/>

<!-- 图标按钮 -->
<Button Style="{StaticResource IconButtonStyle}">
    <TextBlock Text="{StaticResource AddIcon}" 
               Style="{StaticResource MediumIconStyle}"/>
</Button>
```

### 输入控件
```xml
<TextBox Background="{DynamicResource ControlFillColorInputActiveBrush}"
         Foreground="{DynamicResource TextFillColorPrimaryBrush}"
         BorderBrush="{DynamicResource ControlStrokeColorDefaultBrush}"/>
```

### 卡片容器
```xml
<Border Background="{DynamicResource CardBackgroundFillColorDefaultBrush}"
        BorderBrush="{DynamicResource CardStrokeColorDefaultBrush}"
        BorderThickness="1"
        CornerRadius="8"
        Padding="20">
    <!-- 卡片内容 -->
</Border>
```

## 🚀 快速开始

1. **引用资源文件**
   在 `App.xaml` 中添加资源引用：
   ```xml
   <ResourceDictionary.MergedDictionaries>
       <ResourceDictionary Source="Styles/Colors.xaml"/>
       <ResourceDictionary Source="Styles/Fonts.xaml"/>
       <ResourceDictionary Source="Styles/Icons.xaml"/>
       <ResourceDictionary Source="Styles/Themes/Light.xaml"/>
   </ResourceDictionary.MergedDictionaries>
   ```

2. **注册主题服务**
   ```csharp
   services.AddSingleton<IThemeService, ThemeService>();
   ```

3. **使用动态资源**
   ```xml
   <Window Background="{DynamicResource ApplicationPageBackgroundBrush}">
       <TextBlock Foreground="{DynamicResource TextFillColorPrimaryBrush}"/>
   </Window>
   ```

## 📋 最佳实践

1. **始终使用动态资源** - 确保主题切换时UI能正确更新
2. **遵循字体层次** - 使用预定义的字体样式保持一致性
3. **合理使用色彩** - 遵循语义色彩的含义
4. **保持对比度** - 确保文本在所有主题下都有足够的对比度
5. **测试主题切换** - 确保所有UI元素在不同主题下都能正常显示

## 🔧 自定义扩展

### 添加新颜色
在 `Colors.xaml` 中添加新的颜色定义：
```xml
<Color x:Key="CustomColor">#FF5722</Color>
<SolidColorBrush x:Key="CustomBrush" Color="{StaticResource CustomColor}"/>
```

### 添加新字体样式
在 `Fonts.xaml` 中添加新的字体样式：
```xml
<Style x:Key="CustomTextStyle" TargetType="TextBlock">
    <Setter Property="FontFamily" Value="{StaticResource PrimaryFontFamily}"/>
    <Setter Property="FontSize" Value="18"/>
    <Setter Property="FontWeight" Value="Medium"/>
</Style>
```

### 添加新图标
在 `Icons.xaml` 中添加新的图标定义：
```xml
<x:String x:Key="CustomIcon">&#xE123;</x:String>
```

## 🎯 完整使用示例

### 创建一个Fluent风格的窗口

```xml
<Window x:Class="MyApp.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="我的应用"
        Height="600" Width="800"
        Background="{DynamicResource ApplicationPageBackgroundBrush}">

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 标题栏 -->
        <TextBlock Grid.Row="0"
                   Text="欢迎使用Fluent Design"
                   Style="{StaticResource TitleTextStyle}"
                   Foreground="{DynamicResource TextFillColorPrimaryBrush}"
                   Margin="0,0,0,20"/>

        <!-- 内容卡片 -->
        <Border Grid.Row="1"
                Background="{DynamicResource CardBackgroundFillColorDefaultBrush}"
                BorderBrush="{DynamicResource CardStrokeColorDefaultBrush}"
                BorderThickness="1"
                CornerRadius="8"
                Padding="20">
            <StackPanel Spacing="15">
                <TextBlock Text="这是一个使用Fluent Design System的示例"
                           Style="{StaticResource BodyLargeTextStyle}"
                           Foreground="{DynamicResource TextFillColorPrimaryBrush}"/>

                <StackPanel Orientation="Horizontal" Spacing="10">
                    <Button Content="主要操作"
                            Background="{DynamicResource AccentFillColorDefaultBrush}"
                            Foreground="White"
                            BorderThickness="0"
                            Padding="16,8"
                            CornerRadius="4"/>

                    <Button Content="次要操作"
                            Background="{DynamicResource ControlFillColorDefaultBrush}"
                            Foreground="{DynamicResource TextFillColorPrimaryBrush}"
                            BorderBrush="{DynamicResource ControlStrokeColorDefaultBrush}"
                            BorderThickness="1"
                            Padding="16,8"
                            CornerRadius="4"/>
                </StackPanel>
            </StackPanel>
        </Border>

        <!-- 状态栏 -->
        <Border Grid.Row="2"
                Background="{DynamicResource ControlFillColorSecondaryBrush}"
                BorderBrush="{DynamicResource ControlStrokeColorDefaultBrush}"
                BorderThickness="0,1,0,0"
                Padding="10,5"
                Margin="0,20,0,0">
            <TextBlock Text="就绪"
                       Style="{StaticResource CaptionTextStyle}"
                       Foreground="{DynamicResource TextFillColorSecondaryBrush}"/>
        </Border>
    </Grid>
</Window>
```

### 在代码中使用主题服务

```csharp
public partial class MainWindow : Window
{
    private readonly IThemeService _themeService;

    public MainWindow(IThemeService themeService)
    {
        InitializeComponent();
        _themeService = themeService;

        // 监听主题变化
        _themeService.ThemeChanged += OnThemeChanged;
    }

    private void OnThemeChanged(object sender, ThemeChangedEventArgs e)
    {
        // 主题变化时的处理逻辑
        Console.WriteLine($"主题已切换: {e.OldTheme} -> {e.NewTheme}");
    }

    private void ToggleTheme_Click(object sender, RoutedEventArgs e)
    {
        _themeService.ToggleTheme();
    }
}
```

## 🔧 高级自定义

### 创建自定义控件样式

```xml
<!-- 自定义按钮样式 -->
<Style x:Key="CustomButtonStyle" TargetType="Button">
    <Setter Property="Background" Value="{DynamicResource AccentFillColorDefaultBrush}"/>
    <Setter Property="Foreground" Value="White"/>
    <Setter Property="BorderThickness" Value="0"/>
    <Setter Property="Padding" Value="16,8"/>
    <Setter Property="CornerRadius" Value="4"/>
    <Setter Property="FontFamily" Value="{StaticResource PrimaryFontFamily}"/>
    <Setter Property="FontSize" Value="{StaticResource BodyFontSize}"/>
    <Setter Property="FontWeight" Value="{StaticResource FontWeightMedium}"/>
    <Setter Property="Template">
        <Setter.Value>
            <ControlTemplate TargetType="Button">
                <Border Background="{TemplateBinding Background}"
                        BorderBrush="{TemplateBinding BorderBrush}"
                        BorderThickness="{TemplateBinding BorderThickness}"
                        CornerRadius="{TemplateBinding CornerRadius}"
                        Padding="{TemplateBinding Padding}">
                    <ContentPresenter HorizontalAlignment="Center"
                                    VerticalAlignment="Center"/>
                </Border>
                <ControlTemplate.Triggers>
                    <Trigger Property="IsMouseOver" Value="True">
                        <Setter Property="Background" Value="{DynamicResource AccentFillColorSecondaryBrush}"/>
                    </Trigger>
                    <Trigger Property="IsPressed" Value="True">
                        <Setter Property="Background" Value="{DynamicResource AccentFillColorTertiaryBrush}"/>
                    </Trigger>
                    <Trigger Property="IsEnabled" Value="False">
                        <Setter Property="Background" Value="{DynamicResource AccentFillColorDisabledBrush}"/>
                        <Setter Property="Foreground" Value="{DynamicResource TextFillColorDisabledBrush}"/>
                    </Trigger>
                </ControlTemplate.Triggers>
            </ControlTemplate>
        </Setter.Value>
    </Setter>
</Style>
```

### 创建响应式布局

```xml
<Grid>
    <Grid.Style>
        <Style TargetType="Grid">
            <Style.Triggers>
                <!-- 窄屏幕布局 -->
                <DataTrigger Binding="{Binding RelativeSource={RelativeSource AncestorType=Window}, Path=ActualWidth, Converter={StaticResource WidthToLayoutConverter}}" Value="Narrow">
                    <Setter Property="Margin" Value="10"/>
                </DataTrigger>
                <!-- 宽屏幕布局 -->
                <DataTrigger Binding="{Binding RelativeSource={RelativeSource AncestorType=Window}, Path=ActualWidth, Converter={StaticResource WidthToLayoutConverter}}" Value="Wide">
                    <Setter Property="Margin" Value="40"/>
                </DataTrigger>
            </Style.Triggers>
        </Style>
    </Grid.Style>
</Grid>
```

## 🎨 设计原则

### 1. 层次结构
- 使用不同的字体大小和权重建立清晰的信息层次
- 通过颜色对比度突出重要内容
- 合理使用空白空间分隔内容区块

### 2. 一致性
- 统一使用预定义的颜色和字体资源
- 保持控件样式的一致性
- 遵循相同的交互模式

### 3. 可访问性
- 确保足够的颜色对比度
- 支持键盘导航
- 提供适当的焦点指示器

### 4. 响应式设计
- 适配不同的屏幕尺寸
- 支持高DPI显示
- 考虑触摸交互

## 📖 参考资料

- [Microsoft Fluent Design System](https://www.microsoft.com/design/fluent/)
- [Segoe MDL2 Assets Icons](https://docs.microsoft.com/en-us/windows/apps/design/style/segoe-ui-symbol-font)
- [WPF Styling and Templating](https://docs.microsoft.com/en-us/dotnet/desktop/wpf/controls/styling-and-templating)
- [Fluent Design System Colors](https://docs.microsoft.com/en-us/windows/apps/design/style/color)
- [Typography in Fluent Design](https://docs.microsoft.com/en-us/windows/apps/design/style/typography)
