using System.Windows;
using AirMonitor.Services;

namespace AirMonitor.Views;

/// <summary>
/// FluentDesignDemo.xaml 的交互逻辑
/// </summary>
public partial class FluentDesignDemo : Window
{
    private readonly IThemeService _themeService;

    public FluentDesignDemo()
    {
        InitializeComponent();
        
        // 在实际应用中，这应该通过依赖注入获取
        // 这里为了演示目的直接创建实例
        _themeService = new ThemeService(Microsoft.Extensions.Logging.Abstractions.NullLogger<ThemeService>.Instance);
    }

    private void ToggleTheme_Click(object sender, RoutedEventArgs e)
    {
        _themeService.ToggleTheme();
    }
}
