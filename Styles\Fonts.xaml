<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:sys="clr-namespace:System;assembly=mscorlib">

    <!-- ========================================
         Fluent Design System - 字体系统
         基于Segoe UI字体家族的层次化设计
         ======================================== -->

    <!-- 字体家族定义 -->
    <FontFamily x:Key="SegoeUIFontFamily">Segoe UI</FontFamily>
    <FontFamily x:Key="SegoeUIVariableFontFamily">Segoe UI Variable</FontFamily>
    <FontFamily x:Key="PrimaryFontFamily">Segoe UI, Microsoft YaHei UI, Arial, sans-serif</FontFamily>
    <FontFamily x:Key="MonospaceFontFamily">Cascadia Code, Consolas, Courier New, monospace</FontFamily>
    <FontFamily x:Key="IconFontFamily">Segoe MDL2 Assets</FontFamily>

    <!-- Fluent Typography Scale - 字体大小 -->
    <!-- Caption -->
    <sys:Double x:Key="CaptionFontSize">12</sys:Double>

    <!-- Body -->
    <sys:Double x:Key="BodyFontSize">14</sys:Double>
    <sys:Double x:Key="BodyStrongFontSize">14</sys:Double>
    <sys:Double x:Key="BodyLargeFontSize">16</sys:Double>

    <!-- Subtitle -->
    <sys:Double x:Key="SubtitleFontSize">20</sys:Double>

    <!-- Title -->
    <sys:Double x:Key="TitleFontSize">28</sys:Double>
    <sys:Double x:Key="TitleLargeFontSize">40</sys:Double>

    <!-- Display -->
    <sys:Double x:Key="DisplayFontSize">68</sys:Double>

    <!-- 兼容性字体大小 -->
    <sys:Double x:Key="FontSizeSmall">12</sys:Double>
    <sys:Double x:Key="FontSizeNormal">14</sys:Double>
    <sys:Double x:Key="FontSizeMedium">16</sys:Double>
    <sys:Double x:Key="FontSizeLarge">20</sys:Double>
    <sys:Double x:Key="FontSizeExtraLarge">28</sys:Double>
    <sys:Double x:Key="FontSizeTitle">40</sys:Double>
    <sys:Double x:Key="FontSizeHeader">24</sys:Double>

    <!-- 字体权重 -->
    <FontWeight x:Key="FontWeightLight">Light</FontWeight>
    <FontWeight x:Key="FontWeightNormal">Normal</FontWeight>
    <FontWeight x:Key="FontWeightMedium">Medium</FontWeight>
    <FontWeight x:Key="FontWeightSemiBold">SemiBold</FontWeight>
    <FontWeight x:Key="FontWeightBold">Bold</FontWeight>

    <!-- 行高定义 -->
    <sys:Double x:Key="CaptionLineHeight">16</sys:Double>
    <sys:Double x:Key="BodyLineHeight">20</sys:Double>
    <sys:Double x:Key="BodyLargeLineHeight">22</sys:Double>
    <sys:Double x:Key="SubtitleLineHeight">28</sys:Double>
    <sys:Double x:Key="TitleLineHeight">36</sys:Double>
    <sys:Double x:Key="TitleLargeLineHeight">52</sys:Double>
    <sys:Double x:Key="DisplayLineHeight">92</sys:Double>

    <!-- 字符间距 -->
    <sys:Double x:Key="CaptionCharacterSpacing">0</sys:Double>
    <sys:Double x:Key="BodyCharacterSpacing">0</sys:Double>
    <sys:Double x:Key="BodyLargeCharacterSpacing">0</sys:Double>
    <sys:Double x:Key="SubtitleCharacterSpacing">0</sys:Double>
    <sys:Double x:Key="TitleCharacterSpacing">-0.5</sys:Double>
    <sys:Double x:Key="TitleLargeCharacterSpacing">-0.5</sys:Double>
    <sys:Double x:Key="DisplayCharacterSpacing">-1.5</sys:Double>

    <!-- 文本样式定义 -->
    <Style x:Key="CaptionTextStyle" TargetType="TextBlock">
        <Setter Property="FontFamily" Value="{StaticResource PrimaryFontFamily}"/>
        <Setter Property="FontSize" Value="{StaticResource CaptionFontSize}"/>
        <Setter Property="FontWeight" Value="{StaticResource FontWeightNormal}"/>
        <Setter Property="LineHeight" Value="{StaticResource CaptionLineHeight}"/>
    </Style>

    <Style x:Key="BodyTextStyle" TargetType="TextBlock">
        <Setter Property="FontFamily" Value="{StaticResource PrimaryFontFamily}"/>
        <Setter Property="FontSize" Value="{StaticResource BodyFontSize}"/>
        <Setter Property="FontWeight" Value="{StaticResource FontWeightNormal}"/>
        <Setter Property="LineHeight" Value="{StaticResource BodyLineHeight}"/>
    </Style>

    <Style x:Key="BodyStrongTextStyle" TargetType="TextBlock">
        <Setter Property="FontFamily" Value="{StaticResource PrimaryFontFamily}"/>
        <Setter Property="FontSize" Value="{StaticResource BodyStrongFontSize}"/>
        <Setter Property="FontWeight" Value="{StaticResource FontWeightSemiBold}"/>
        <Setter Property="LineHeight" Value="{StaticResource BodyLineHeight}"/>
    </Style>

    <Style x:Key="BodyLargeTextStyle" TargetType="TextBlock">
        <Setter Property="FontFamily" Value="{StaticResource PrimaryFontFamily}"/>
        <Setter Property="FontSize" Value="{StaticResource BodyLargeFontSize}"/>
        <Setter Property="FontWeight" Value="{StaticResource FontWeightNormal}"/>
        <Setter Property="LineHeight" Value="{StaticResource BodyLargeLineHeight}"/>
    </Style>

    <Style x:Key="SubtitleTextStyle" TargetType="TextBlock">
        <Setter Property="FontFamily" Value="{StaticResource PrimaryFontFamily}"/>
        <Setter Property="FontSize" Value="{StaticResource SubtitleFontSize}"/>
        <Setter Property="FontWeight" Value="{StaticResource FontWeightSemiBold}"/>
        <Setter Property="LineHeight" Value="{StaticResource SubtitleLineHeight}"/>
    </Style>

    <Style x:Key="TitleTextStyle" TargetType="TextBlock">
        <Setter Property="FontFamily" Value="{StaticResource PrimaryFontFamily}"/>
        <Setter Property="FontSize" Value="{StaticResource TitleFontSize}"/>
        <Setter Property="FontWeight" Value="{StaticResource FontWeightSemiBold}"/>
        <Setter Property="LineHeight" Value="{StaticResource TitleLineHeight}"/>
    </Style>

    <Style x:Key="TitleLargeTextStyle" TargetType="TextBlock">
        <Setter Property="FontFamily" Value="{StaticResource PrimaryFontFamily}"/>
        <Setter Property="FontSize" Value="{StaticResource TitleLargeFontSize}"/>
        <Setter Property="FontWeight" Value="{StaticResource FontWeightSemiBold}"/>
        <Setter Property="LineHeight" Value="{StaticResource TitleLargeLineHeight}"/>
    </Style>

    <Style x:Key="DisplayTextStyle" TargetType="TextBlock">
        <Setter Property="FontFamily" Value="{StaticResource PrimaryFontFamily}"/>
        <Setter Property="FontSize" Value="{StaticResource DisplayFontSize}"/>
        <Setter Property="FontWeight" Value="{StaticResource FontWeightSemiBold}"/>
        <Setter Property="LineHeight" Value="{StaticResource DisplayLineHeight}"/>
    </Style>

</ResourceDictionary>
