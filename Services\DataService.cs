using System.Text.Json;

namespace AirMonitor.Services;

/// <summary>
/// 数据服务实现类
/// </summary>
public class DataService : IDataService
{
    private readonly ILogger<DataService> _logger;
    private readonly IConfiguration _configuration;
    private readonly string _dataDirectory;

    public DataService(ILogger<DataService> logger, IConfiguration configuration)
    {
        _logger = logger;
        _configuration = configuration;
        _dataDirectory = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Data");
    }

    public async Task InitializeAsync()
    {
        try
        {
            if (!Directory.Exists(_dataDirectory))
            {
                Directory.CreateDirectory(_dataDirectory);
                _logger.LogInformation("创建数据目录: {DataDirectory}", _dataDirectory);
            }

            _logger.LogInformation("数据服务初始化完成");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "数据服务初始化失败");
            throw;
        }
    }

    public async Task<T?> GetDataAsync<T>(string key) where T : class
    {
        try
        {
            var filePath = GetFilePath(key);
            if (!File.Exists(filePath))
            {
                _logger.LogDebug("数据文件不存在: {FilePath}", filePath);
                return null;
            }

            var json = await File.ReadAllTextAsync(filePath);
            var data = JsonSerializer.Deserialize<T>(json);
            
            _logger.LogDebug("成功读取数据: {Key}", key);
            return data;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "读取数据失败: {Key}", key);
            return null;
        }
    }

    public async Task SaveDataAsync<T>(string key, T data) where T : class
    {
        try
        {
            var filePath = GetFilePath(key);
            var json = JsonSerializer.Serialize(data, new JsonSerializerOptions 
            { 
                WriteIndented = true,
                Encoder = System.Text.Encodings.Web.JavaScriptEncoder.UnsafeRelaxedJsonEscaping
            });
            
            await File.WriteAllTextAsync(filePath, json);
            _logger.LogDebug("成功保存数据: {Key}", key);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "保存数据失败: {Key}", key);
            throw;
        }
    }

    public async Task DeleteDataAsync(string key)
    {
        try
        {
            var filePath = GetFilePath(key);
            if (File.Exists(filePath))
            {
                File.Delete(filePath);
                _logger.LogDebug("成功删除数据: {Key}", key);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "删除数据失败: {Key}", key);
            throw;
        }
    }

    public async Task<bool> ExistsAsync(string key)
    {
        var filePath = GetFilePath(key);
        return File.Exists(filePath);
    }

    private string GetFilePath(string key)
    {
        var fileName = $"{key}.json";
        return Path.Combine(_dataDirectory, fileName);
    }
}
