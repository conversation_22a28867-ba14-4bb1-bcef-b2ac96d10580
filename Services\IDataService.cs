namespace AirMonitor.Services;

/// <summary>
/// 数据服务接口，提供数据访问和管理功能
/// </summary>
public interface IDataService
{
    /// <summary>
    /// 初始化数据服务
    /// </summary>
    /// <returns></returns>
    Task InitializeAsync();

    /// <summary>
    /// 获取数据
    /// </summary>
    /// <typeparam name="T">数据类型</typeparam>
    /// <param name="key">数据键</param>
    /// <returns></returns>
    Task<T?> GetDataAsync<T>(string key) where T : class;

    /// <summary>
    /// 保存数据
    /// </summary>
    /// <typeparam name="T">数据类型</typeparam>
    /// <param name="key">数据键</param>
    /// <param name="data">数据对象</param>
    /// <returns></returns>
    Task SaveDataAsync<T>(string key, T data) where T : class;

    /// <summary>
    /// 删除数据
    /// </summary>
    /// <param name="key">数据键</param>
    /// <returns></returns>
    Task DeleteDataAsync(string key);

    /// <summary>
    /// 检查数据是否存在
    /// </summary>
    /// <param name="key">数据键</param>
    /// <returns></returns>
    Task<bool> ExistsAsync(string key);
}
