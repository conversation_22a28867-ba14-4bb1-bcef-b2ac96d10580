using System.Windows;
using System.Windows.Threading;
using AirMonitor.Services;
using AirMonitor.Views;
using Microsoft.Extensions.Logging;

namespace AirMonitor;

/// <summary>
/// MainWindow.xaml 的交互逻辑
/// </summary>
public partial class MainWindow : Window
{
    private readonly DispatcherTimer _timer;
    private readonly IThemeService _themeService;

    public MainWindow()
    {
        InitializeComponent();

        // 在实际应用中，这应该通过依赖注入获取
        // 这里为了演示目的直接创建实例
        _themeService = new ThemeService(Microsoft.Extensions.Logging.Abstractions.NullLogger<ThemeService>.Instance);

        // 初始化时间显示定时器
        _timer = new DispatcherTimer
        {
            Interval = TimeSpan.FromSeconds(1)
        };
        _timer.Tick += Timer_Tick;
        _timer.Start();
    }

    private void Timer_Tick(object? sender, EventArgs e)
    {
        // 更新时间显示
        if (TimeDisplay != null)
        {
            TimeDisplay.Text = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
        }
    }

    private void ToggleTheme_Click(object sender, RoutedEventArgs e)
    {
        _themeService.ToggleTheme();
    }

    private void ShowFluentDemo_Click(object sender, RoutedEventArgs e)
    {
        var demoWindow = new FluentDesignDemo();
        demoWindow.Show();
    }

    protected override void OnClosed(EventArgs e)
    {
        _timer?.Stop();
        base.OnClosed(e);
    }
}
