# AirMonitor WPF 应用程序

这是一个基于 WPF 和 .NET 9.0 的桌面应用程序，采用现代化的 MVVM 架构和依赖注入模式。

## 项目特性

- ✅ **.NET 9.0** - 使用最新的 .NET 框架
- ✅ **WPF** - Windows Presentation Foundation 桌面应用
- ✅ **MVVM 架构** - 使用 CommunityToolkit.Mvvm 实现现代化 MVVM 模式
- ✅ **依赖注入** - 使用 .NET 9 内置的依赖注入容器
- ✅ **日志系统** - 集成 Serilog，支持控制台和文件日志
- ✅ **配置系统** - 支持 appsettings.json 和用户配置
- ✅ **现代化 UI** - Material Design 风格的样式系统

## 项目结构

```
AirMonitor/
├── Models/                 # 数据模型
├── ViewModels/            # 视图模型
│   └── MainViewModel.cs   # 主窗口视图模型
├── Views/                 # 视图
├── Services/              # 服务层
│   ├── IDataService.cs    # 数据服务接口
│   ├── DataService.cs     # 数据服务实现
│   ├── IConfigurationService.cs  # 配置服务接口
│   └── ConfigurationService.cs   # 配置服务实现
├── Converters/            # 值转换器
│   └── BooleanToVisibilityConverter.cs
├── Controls/              # 自定义控件
├── Behaviors/             # 行为
├── Styles/                # 样式资源
│   ├── Colors.xaml        # 颜色定义
│   ├── Fonts.xaml         # 字体定义
│   ├── ButtonStyles.xaml  # 按钮样式
│   └── TextStyles.xaml    # 文本样式
├── Resources/             # 其他资源
├── Helpers/               # 辅助类
├── Extensions/            # 扩展方法
├── App.xaml              # 应用程序资源
├── App.xaml.cs           # 应用程序启动逻辑
├── MainWindow.xaml       # 主窗口界面
├── MainWindow.xaml.cs    # 主窗口代码
├── appsettings.json      # 应用程序配置
└── AirMonitor.csproj     # 项目文件
```

## 核心技术栈

### 框架和库
- **.NET 9.0** - 目标框架
- **WPF** - UI 框架
- **CommunityToolkit.Mvvm 8.4.0** - MVVM 框架
- **Microsoft.Extensions.Hosting** - 主机和依赖注入
- **Microsoft.Extensions.Logging** - 日志框架
- **Microsoft.Extensions.Configuration** - 配置系统
- **Serilog** - 结构化日志库

### 架构模式
- **MVVM (Model-View-ViewModel)** - 使用 CommunityToolkit.Mvvm 实现
- **依赖注入 (DI)** - 使用 .NET 9 内置容器
- **服务定位器模式** - 通过 DI 容器管理服务生命周期

## 快速开始

### 环境要求
- Visual Studio 2022 (17.8 或更高版本)
- .NET 9.0 SDK
- Windows 10/11

### 构建和运行
```bash
# 克隆项目
git clone <repository-url>
cd AirMonitor

# 还原依赖包
dotnet restore

# 构建项目
dotnet build

# 运行应用程序
dotnet run
```

## 配置说明

### appsettings.json
应用程序的主要配置文件，包含：
- 日志配置
- 应用程序信息
- 数据库连接
- UI 设置

### 用户配置
运行时配置会保存到 `user-settings.json` 文件中，包含用户的个性化设置。

## 服务说明

### IDataService / DataService
- 提供数据持久化功能
- 支持 JSON 格式的数据存储
- 自动创建数据目录

### IConfigurationService / ConfigurationService
- 管理应用程序配置
- 支持运行时配置更改
- 自动保存用户设置

## MVVM 实现

### ViewModel 特性
使用 CommunityToolkit.Mvvm 的现代化特性：
- `[ObservableProperty]` - 自动实现属性通知
- `[RelayCommand]` - 自动创建命令
- `[NotifyPropertyChangedFor]` - 相关属性通知
- `[NotifyCanExecuteChangedFor]` - 命令可执行状态通知

### 示例代码
```csharp
public partial class MainViewModel : ObservableObject
{
    [ObservableProperty]
    private string _title = "AirMonitor";
    
    [RelayCommand]
    private async Task RefreshAsync()
    {
        // 刷新逻辑
    }
}
```

## 样式系统

### 颜色主题
- 主色调：Material Blue (#2196F3)
- 辅助色：Material Pink (#FF4081)
- 支持亮色主题

### 字体系统
- 主字体：Microsoft YaHei UI, Segoe UI
- 等宽字体：Consolas, Courier New
- 多种字体大小和权重

### 组件样式
- 按钮样式：主要、次要、工具栏、危险、成功
- 文本样式：标题、正文、标签、提示、错误等
- 容器样式：窗口、边框、工作区等

## 日志系统

### 日志配置
- 控制台输出：开发调试使用
- 文件输出：logs/airmonitor-{date}.log
- 日志级别：Information（默认）
- 自动滚动：按天分割，保留7天

### 日志使用
```csharp
public class MyService
{
    private readonly ILogger<MyService> _logger;
    
    public MyService(ILogger<MyService> logger)
    {
        _logger = logger;
    }
    
    public void DoSomething()
    {
        _logger.LogInformation("执行某项操作");
    }
}
```

## 开发规范

请参考项目根目录的 WPF 开发规则文档，严格遵循以下规范：
- MVVM 架构模式
- 依赖注入使用
- 样式和资源管理
- 命名约定
- 代码组织结构

## 许可证

本项目采用 MIT 许可证。详情请参阅 LICENSE 文件。
