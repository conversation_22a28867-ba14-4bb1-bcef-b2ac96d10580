using System.Text.Json;

namespace AirMonitor.Services;

/// <summary>
/// 配置服务实现类
/// </summary>
public class ConfigurationService : IConfigurationService
{
    private readonly IConfiguration _configuration;
    private readonly ILogger<ConfigurationService> _logger;
    private readonly Dictionary<string, object> _runtimeSettings;
    private readonly string _userConfigPath;

    public ConfigurationService(IConfiguration configuration, ILogger<ConfigurationService> logger)
    {
        _configuration = configuration;
        _logger = logger;
        _runtimeSettings = new Dictionary<string, object>();
        _userConfigPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "user-settings.json");
        
        LoadUserSettings();
    }

    public string ApplicationName => GetValue("Application:Name", "AirMonitor");
    public string ApplicationVersion => GetValue("Application:Version", "1.0.0");
    public string Environment => GetValue("Application:Environment", "Development");

    public T GetValue<T>(string key, T defaultValue = default!)
    {
        try
        {
            // 首先检查运行时设置
            if (_runtimeSettings.TryGetValue(key, out var runtimeValue) && runtimeValue is T)
            {
                return (T)runtimeValue;
            }

            // 然后检查配置文件
            var configValue = _configuration.GetValue<T>(key);
            if (configValue != null && !configValue.Equals(default(T)))
            {
                return configValue;
            }

            return defaultValue;
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "获取配置值失败，使用默认值: {Key}", key);
            return defaultValue;
        }
    }

    public void SetValue<T>(string key, T value)
    {
        try
        {
            _runtimeSettings[key] = value!;
            _logger.LogDebug("设置配置值: {Key} = {Value}", key, value);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "设置配置值失败: {Key}", key);
            throw;
        }
    }

    public string? GetConnectionString(string name)
    {
        return _configuration.GetConnectionString(name);
    }

    public async Task SaveChangesAsync()
    {
        try
        {
            var json = JsonSerializer.Serialize(_runtimeSettings, new JsonSerializerOptions 
            { 
                WriteIndented = true,
                Encoder = System.Text.Encodings.Web.JavaScriptEncoder.UnsafeRelaxedJsonEscaping
            });
            
            await File.WriteAllTextAsync(_userConfigPath, json);
            _logger.LogInformation("用户配置已保存到: {Path}", _userConfigPath);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "保存用户配置失败");
            throw;
        }
    }

    public void Reload()
    {
        try
        {
            if (_configuration is IConfigurationRoot configRoot)
            {
                configRoot.Reload();
            }
            
            LoadUserSettings();
            _logger.LogInformation("配置已重新加载");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "重新加载配置失败");
            throw;
        }
    }

    private void LoadUserSettings()
    {
        try
        {
            if (File.Exists(_userConfigPath))
            {
                var json = File.ReadAllText(_userConfigPath);
                var settings = JsonSerializer.Deserialize<Dictionary<string, object>>(json);
                
                if (settings != null)
                {
                    foreach (var kvp in settings)
                    {
                        _runtimeSettings[kvp.Key] = kvp.Value;
                    }
                }
                
                _logger.LogDebug("用户配置已加载: {Count} 项", _runtimeSettings.Count);
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "加载用户配置失败，将使用默认配置");
        }
    }
}
