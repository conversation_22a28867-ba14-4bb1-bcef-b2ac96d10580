using System;

namespace AirMonitor.Services;

/// <summary>
/// 主题服务接口
/// 提供主题切换和管理功能
/// </summary>
public interface IThemeService
{
    /// <summary>
    /// 当前主题
    /// </summary>
    AppTheme CurrentTheme { get; }

    /// <summary>
    /// 主题变更事件
    /// </summary>
    event EventHandler<ThemeChangedEventArgs> ThemeChanged;

    /// <summary>
    /// 设置应用主题
    /// </summary>
    /// <param name="theme">要设置的主题</param>
    void SetTheme(AppTheme theme);

    /// <summary>
    /// 切换主题（在亮色和暗色之间切换）
    /// </summary>
    void ToggleTheme();

    /// <summary>
    /// 根据系统主题自动设置
    /// </summary>
    void SetSystemTheme();

    /// <summary>
    /// 获取系统当前主题
    /// </summary>
    /// <returns>系统主题</returns>
    AppTheme GetSystemTheme();
}

/// <summary>
/// 应用主题枚举
/// </summary>
public enum AppTheme
{
    /// <summary>
    /// 亮色主题
    /// </summary>
    Light,

    /// <summary>
    /// 暗色主题
    /// </summary>
    Dark,

    /// <summary>
    /// 跟随系统
    /// </summary>
    System
}

/// <summary>
/// 主题变更事件参数
/// </summary>
public class ThemeChangedEventArgs : EventArgs
{
    /// <summary>
    /// 新主题
    /// </summary>
    public AppTheme NewTheme { get; }

    /// <summary>
    /// 旧主题
    /// </summary>
    public AppTheme OldTheme { get; }

    public ThemeChangedEventArgs(AppTheme newTheme, AppTheme oldTheme)
    {
        NewTheme = newTheme;
        OldTheme = oldTheme;
    }
}
