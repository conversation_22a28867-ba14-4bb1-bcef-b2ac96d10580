<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">

    <!-- ========================================
         Fluent Design System - 亮色主题
         Light Theme Color Definitions
         ======================================== -->

    <!-- 应用程序背景色 -->
    <Color x:Key="ApplicationPageBackgroundThemeColor">#FFFFFF</Color>
    <SolidColorBrush x:Key="ApplicationPageBackgroundThemeBrush" Color="{StaticResource ApplicationPageBackgroundThemeColor}"/>

    <!-- 系统背景色 -->
    <Color x:Key="SystemBackgroundColor">#F3F2F1</Color>
    <Color x:Key="SystemChromeLowColor">#F3F2F1</Color>
    <Color x:Key="SystemChromeMediumLowColor">#F3F2F1</Color>
    <Color x:Key="SystemChromeMediumColor">#E1DFDD</Color>
    <Color x:Key="SystemChromeHighColor">#CCCCCC</Color>
    <Color x:Key="SystemChromeAltLowColor">#F3F2F1</Color>
    <Color x:Key="SystemChromeAltMediumLowColor">#E1DFDD</Color>
    <Color x:Key="SystemChromeAltMediumColor">#C8C6C4</Color>
    <Color x:Key="SystemChromeAltHighColor">#8A8886</Color>

    <!-- 系统文本色 -->
    <Color x:Key="SystemBaseHighColor">#000000</Color>
    <Color x:Key="SystemBaseMediumHighColor">#323130</Color>
    <Color x:Key="SystemBaseMediumColor">#605E5C</Color>
    <Color x:Key="SystemBaseMediumLowColor">#8A8886</Color>
    <Color x:Key="SystemBaseLowColor">#C8C6C4</Color>
    <Color x:Key="SystemAltHighColor">#FFFFFF</Color>
    <Color x:Key="SystemAltMediumHighColor">#F3F2F1</Color>
    <Color x:Key="SystemAltMediumColor">#E1DFDD</Color>
    <Color x:Key="SystemAltMediumLowColor">#C8C6C4</Color>
    <Color x:Key="SystemAltLowColor">#8A8886</Color>

    <!-- 控件背景色 -->
    <Color x:Key="ControlFillColorDefault">#B3FFFFFF</Color>
    <Color x:Key="ControlFillColorSecondary">#80F9F9F9</Color>
    <Color x:Key="ControlFillColorTertiary">#4DF9F9F9</Color>
    <Color x:Key="ControlFillColorDisabled">#4DF9F9F9</Color>
    <Color x:Key="ControlFillColorTransparent">#00FFFFFF</Color>
    <Color x:Key="ControlFillColorInputActive">#FFFFFF</Color>

    <!-- 控件边框色 -->
    <Color x:Key="ControlStrokeColorDefault">#0F000000</Color>
    <Color x:Key="ControlStrokeColorSecondary">#29000000</Color>
    <Color x:Key="ControlStrokeColorOnAccentDefault">#14FFFFFF</Color>
    <Color x:Key="ControlStrokeColorOnAccentSecondary">#23000000</Color>
    <Color x:Key="ControlStrokeColorOnAccentTertiary">#37000000</Color>
    <Color x:Key="ControlStrokeColorOnAccentDisabled">#0F000000</Color>

    <!-- 控件文本色 -->
    <Color x:Key="TextFillColorPrimary">#E4000000</Color>
    <Color x:Key="TextFillColorSecondary">#9E000000</Color>
    <Color x:Key="TextFillColorTertiary">#72000000</Color>
    <Color x:Key="TextFillColorDisabled">#5C000000</Color>
    <Color x:Key="TextFillColorInverse">#FFFFFF</Color>

    <!-- 强调色相关 -->
    <Color x:Key="AccentFillColorDefault">{StaticResource SystemAccentColor}</Color>
    <Color x:Key="AccentFillColorSecondary">{StaticResource SystemAccentColorLight1}</Color>
    <Color x:Key="AccentFillColorTertiary">{StaticResource SystemAccentColorLight2}</Color>
    <Color x:Key="AccentFillColorDisabled">#37000000</Color>

    <!-- 卡片和表面色 -->
    <Color x:Key="CardBackgroundFillColorDefault">#B3FFFFFF</Color>
    <Color x:Key="CardBackgroundFillColorSecondary">#80F6F6F6</Color>
    <Color x:Key="CardStrokeColorDefault">#0F000000</Color>
    <Color x:Key="CardStrokeColorDefaultSolid">#E5E5E5</Color>

    <!-- 烟雾和阴影色 -->
    <Color x:Key="SmokeFillColorDefault">#4D000000</Color>
    <Color x:Key="LayerFillColorDefault">#80FFFFFF</Color>
    <Color x:Key="LayerFillColorAlt">#FFFFFF</Color>
    <Color x:Key="LayerOnAcrylicFillColorDefault">#40FFFFFF</Color>
    <Color x:Key="LayerOnAccentAcrylicFillColorDefault">#40FFFFFF</Color>

    <!-- 系统注意色 -->
    <Color x:Key="SystemAttentionColor">#005FB8</Color>
    <Color x:Key="SystemNeutralColor">#8A8886</Color>
    <Color x:Key="SystemSolidAttentionColor">#0078D4</Color>

    <!-- 画刷资源 -->
    <SolidColorBrush x:Key="ApplicationPageBackgroundBrush" Color="{StaticResource ApplicationPageBackgroundThemeColor}"/>
    <SolidColorBrush x:Key="SystemBackgroundBrush" Color="{StaticResource SystemBackgroundColor}"/>
    
    <SolidColorBrush x:Key="SystemBaseHighBrush" Color="{StaticResource SystemBaseHighColor}"/>
    <SolidColorBrush x:Key="SystemBaseMediumHighBrush" Color="{StaticResource SystemBaseMediumHighColor}"/>
    <SolidColorBrush x:Key="SystemBaseMediumBrush" Color="{StaticResource SystemBaseMediumColor}"/>
    <SolidColorBrush x:Key="SystemBaseMediumLowBrush" Color="{StaticResource SystemBaseMediumLowColor}"/>
    <SolidColorBrush x:Key="SystemBaseLowBrush" Color="{StaticResource SystemBaseLowColor}"/>
    
    <SolidColorBrush x:Key="ControlFillColorDefaultBrush" Color="{StaticResource ControlFillColorDefault}"/>
    <SolidColorBrush x:Key="ControlFillColorSecondaryBrush" Color="{StaticResource ControlFillColorSecondary}"/>
    <SolidColorBrush x:Key="ControlFillColorTertiaryBrush" Color="{StaticResource ControlFillColorTertiary}"/>
    <SolidColorBrush x:Key="ControlFillColorDisabledBrush" Color="{StaticResource ControlFillColorDisabled}"/>
    <SolidColorBrush x:Key="ControlFillColorTransparentBrush" Color="{StaticResource ControlFillColorTransparent}"/>
    <SolidColorBrush x:Key="ControlFillColorInputActiveBrush" Color="{StaticResource ControlFillColorInputActive}"/>
    
    <SolidColorBrush x:Key="ControlStrokeColorDefaultBrush" Color="{StaticResource ControlStrokeColorDefault}"/>
    <SolidColorBrush x:Key="ControlStrokeColorSecondaryBrush" Color="{StaticResource ControlStrokeColorSecondary}"/>
    
    <SolidColorBrush x:Key="TextFillColorPrimaryBrush" Color="{StaticResource TextFillColorPrimary}"/>
    <SolidColorBrush x:Key="TextFillColorSecondaryBrush" Color="{StaticResource TextFillColorSecondary}"/>
    <SolidColorBrush x:Key="TextFillColorTertiaryBrush" Color="{StaticResource TextFillColorTertiary}"/>
    <SolidColorBrush x:Key="TextFillColorDisabledBrush" Color="{StaticResource TextFillColorDisabled}"/>
    <SolidColorBrush x:Key="TextFillColorInverseBrush" Color="{StaticResource TextFillColorInverse}"/>
    
    <SolidColorBrush x:Key="AccentFillColorDefaultBrush" Color="{StaticResource AccentFillColorDefault}"/>
    <SolidColorBrush x:Key="AccentFillColorSecondaryBrush" Color="{StaticResource AccentFillColorSecondary}"/>
    <SolidColorBrush x:Key="AccentFillColorTertiaryBrush" Color="{StaticResource AccentFillColorTertiary}"/>
    <SolidColorBrush x:Key="AccentFillColorDisabledBrush" Color="{StaticResource AccentFillColorDisabled}"/>
    
    <SolidColorBrush x:Key="CardBackgroundFillColorDefaultBrush" Color="{StaticResource CardBackgroundFillColorDefault}"/>
    <SolidColorBrush x:Key="CardBackgroundFillColorSecondaryBrush" Color="{StaticResource CardBackgroundFillColorSecondary}"/>
    <SolidColorBrush x:Key="CardStrokeColorDefaultBrush" Color="{StaticResource CardStrokeColorDefault}"/>
    <SolidColorBrush x:Key="CardStrokeColorDefaultSolidBrush" Color="{StaticResource CardStrokeColorDefaultSolid}"/>
    
    <SolidColorBrush x:Key="SmokeFillColorDefaultBrush" Color="{StaticResource SmokeFillColorDefault}"/>
    <SolidColorBrush x:Key="LayerFillColorDefaultBrush" Color="{StaticResource LayerFillColorDefault}"/>
    <SolidColorBrush x:Key="LayerFillColorAltBrush" Color="{StaticResource LayerFillColorAlt}"/>

    <!-- 语义色画刷 -->
    <SolidColorBrush x:Key="SuccessBrush" Color="{StaticResource SharedGreen}"/>
    <SolidColorBrush x:Key="WarningBrush" Color="{StaticResource SharedYellow}"/>
    <SolidColorBrush x:Key="ErrorBrush" Color="{StaticResource SharedRed}"/>
    <SolidColorBrush x:Key="InfoBrush" Color="{StaticResource SharedBlue}"/>

</ResourceDictionary>
