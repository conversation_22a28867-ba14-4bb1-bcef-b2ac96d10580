namespace AirMonitor.Services;

/// <summary>
/// 配置服务接口，提供应用程序配置管理功能
/// </summary>
public interface IConfigurationService
{
    /// <summary>
    /// 获取配置值
    /// </summary>
    /// <typeparam name="T">配置值类型</typeparam>
    /// <param name="key">配置键</param>
    /// <param name="defaultValue">默认值</param>
    /// <returns></returns>
    T GetValue<T>(string key, T defaultValue = default!);

    /// <summary>
    /// 设置配置值
    /// </summary>
    /// <typeparam name="T">配置值类型</typeparam>
    /// <param name="key">配置键</param>
    /// <param name="value">配置值</param>
    void SetValue<T>(string key, T value);

    /// <summary>
    /// 获取连接字符串
    /// </summary>
    /// <param name="name">连接字符串名称</param>
    /// <returns></returns>
    string? GetConnectionString(string name);

    /// <summary>
    /// 获取应用程序名称
    /// </summary>
    string ApplicationName { get; }

    /// <summary>
    /// 获取应用程序版本
    /// </summary>
    string ApplicationVersion { get; }

    /// <summary>
    /// 获取当前环境
    /// </summary>
    string Environment { get; }

    /// <summary>
    /// 保存配置更改
    /// </summary>
    /// <returns></returns>
    Task SaveChangesAsync();

    /// <summary>
    /// 重新加载配置
    /// </summary>
    void Reload();
}
