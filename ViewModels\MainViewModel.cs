using AirMonitor.Services;
using System.Windows;

namespace AirMonitor.ViewModels;

/// <summary>
/// 主窗口视图模型
/// </summary>
public partial class MainViewModel : ObservableObject
{
    private readonly ILogger<MainViewModel> _logger;
    private readonly IConfigurationService _configurationService;
    private readonly IDataService _dataService;

    public MainViewModel(
        ILogger<MainViewModel> logger,
        IConfigurationService configurationService,
        IDataService dataService)
    {
        _logger = logger;
        _configurationService = configurationService;
        _dataService = dataService;

        // 初始化属性
        Title = _configurationService.ApplicationName;
        Version = _configurationService.ApplicationVersion;
        Environment = _configurationService.Environment;
        StatusMessage = "应用程序已启动";

        _logger.LogInformation("MainViewModel 已初始化");
    }

    #region 属性

    /// <summary>
    /// 应用程序标题
    /// </summary>
    [ObservableProperty]
    private string _title = "AirMonitor";

    /// <summary>
    /// 应用程序版本
    /// </summary>
    [ObservableProperty]
    private string _version = "1.0.0";

    /// <summary>
    /// 运行环境
    /// </summary>
    [ObservableProperty]
    private string _environment = "Development";

    /// <summary>
    /// 状态消息
    /// </summary>
    [ObservableProperty]
    private string _statusMessage = "就绪";

    /// <summary>
    /// 是否正在加载
    /// </summary>
    [ObservableProperty]
    private bool _isLoading;

    /// <summary>
    /// 当前用户
    /// </summary>
    [ObservableProperty]
    private string _currentUser = System.Environment.UserName;

    /// <summary>
    /// 完整标题（包含版本信息）
    /// </summary>
    public string FullTitle => $"{Title} v{Version} ({Environment})";

    #endregion

    #region 命令

    /// <summary>
    /// 刷新命令
    /// </summary>
    [RelayCommand]
    private async Task RefreshAsync()
    {
        try
        {
            IsLoading = true;
            StatusMessage = "正在刷新...";

            _logger.LogInformation("开始刷新数据");

            // 模拟刷新操作
            await Task.Delay(1000);

            StatusMessage = "刷新完成";
            _logger.LogInformation("数据刷新完成");
        }
        catch (Exception ex)
        {
            StatusMessage = $"刷新失败: {ex.Message}";
            _logger.LogError(ex, "刷新数据时发生错误");
        }
        finally
        {
            IsLoading = false;
        }
    }

    /// <summary>
    /// 保存设置命令
    /// </summary>
    [RelayCommand]
    private async Task SaveSettingsAsync()
    {
        try
        {
            IsLoading = true;
            StatusMessage = "正在保存设置...";

            _logger.LogInformation("开始保存设置");

            // 保存一些示例设置
            _configurationService.SetValue("UI:LastUser", CurrentUser);
            _configurationService.SetValue("UI:LastSaveTime", DateTime.Now);
            
            await _configurationService.SaveChangesAsync();

            StatusMessage = "设置已保存";
            _logger.LogInformation("设置保存完成");
        }
        catch (Exception ex)
        {
            StatusMessage = $"保存设置失败: {ex.Message}";
            _logger.LogError(ex, "保存设置时发生错误");
        }
        finally
        {
            IsLoading = false;
        }
    }

    /// <summary>
    /// 退出应用程序命令
    /// </summary>
    [RelayCommand]
    private void ExitApplication()
    {
        try
        {
            _logger.LogInformation("用户请求退出应用程序");
            Application.Current.Shutdown();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "退出应用程序时发生错误");
        }
    }

    /// <summary>
    /// 显示关于对话框命令
    /// </summary>
    [RelayCommand]
    private void ShowAbout()
    {
        try
        {
            var message = $"{Title}\n版本: {Version}\n环境: {Environment}\n用户: {CurrentUser}";
            MessageBox.Show(message, "关于", MessageBoxButton.OK, MessageBoxImage.Information);
            
            _logger.LogDebug("显示关于对话框");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "显示关于对话框时发生错误");
        }
    }

    #endregion

    #region 方法

    /// <summary>
    /// 更新状态消息
    /// </summary>
    /// <param name="message">状态消息</param>
    public void UpdateStatus(string message)
    {
        StatusMessage = message;
        _logger.LogDebug("状态消息已更新: {Message}", message);
    }

    #endregion
}
