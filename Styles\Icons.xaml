<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">

    <!-- ========================================
         Fluent Design System - 图标资源
         基于Segoe MDL2 Assets字体的图标定义
         ======================================== -->

    <!-- 图标字体 -->
    <FontFamily x:Key="SymbolThemeFontFamily">Segoe MDL2 Assets</FontFamily>

    <!-- 常用图标字符 -->
    <!-- 导航图标 -->
    <x:String x:Key="HomeIcon">&#xE80F;</x:String>
    <x:String x:Key="BackIcon">&#xE72B;</x:String>
    <x:String x:Key="ForwardIcon">&#xE72A;</x:String>
    <x:String x:Key="UpIcon">&#xE70E;</x:String>
    <x:String x:Key="DownIcon">&#xE70D;</x:String>
    <x:String x:Key="LeftIcon">&#xE76B;</x:String>
    <x:String x:Key="RightIcon">&#xE76C;</x:String>
    <x:String x:Key="RefreshIcon">&#xE72C;</x:String>

    <!-- 操作图标 -->
    <x:String x:Key="AddIcon">&#xE710;</x:String>
    <x:String x:Key="RemoveIcon">&#xE738;</x:String>
    <x:String x:Key="DeleteIcon">&#xE74D;</x:String>
    <x:String x:Key="EditIcon">&#xE70F;</x:String>
    <x:String x:Key="SaveIcon">&#xE74E;</x:String>
    <x:String x:Key="CancelIcon">&#xE711;</x:String>
    <x:String x:Key="AcceptIcon">&#xE8FB;</x:String>
    <x:String x:Key="CopyIcon">&#xE8C8;</x:String>
    <x:String x:Key="PasteIcon">&#xE77F;</x:String>
    <x:String x:Key="CutIcon">&#xE8C6;</x:String>
    <x:String x:Key="UndoIcon">&#xE7A7;</x:String>
    <x:String x:Key="RedoIcon">&#xE7A6;</x:String>

    <!-- 文件图标 -->
    <x:String x:Key="FolderIcon">&#xE8B7;</x:String>
    <x:String x:Key="FileIcon">&#xE8A5;</x:String>
    <x:String x:Key="DocumentIcon">&#xE8A5;</x:String>
    <x:String x:Key="OpenIcon">&#xE8E5;</x:String>
    <x:String x:Key="OpenFileIcon">&#xE8E5;</x:String>
    <x:String x:Key="SaveAsIcon">&#xE792;</x:String>

    <!-- 媒体图标 -->
    <x:String x:Key="PlayIcon">&#xE768;</x:String>
    <x:String x:Key="PauseIcon">&#xE769;</x:String>
    <x:String x:Key="StopIcon">&#xE71A;</x:String>
    <x:String x:Key="VolumeIcon">&#xE767;</x:String>
    <x:String x:Key="MuteIcon">&#xE74F;</x:String>

    <!-- 通信图标 -->
    <x:String x:Key="MailIcon">&#xE715;</x:String>
    <x:String x:Key="SendIcon">&#xE724;</x:String>
    <x:String x:Key="PhoneIcon">&#xE717;</x:String>
    <x:String x:Key="ContactIcon">&#xE77B;</x:String>

    <!-- 设置图标 -->
    <x:String x:Key="SettingsIcon">&#xE713;</x:String>
    <x:String x:Key="PreferencesIcon">&#xE713;</x:String>
    <x:String x:Key="OptionsIcon">&#xE713;</x:String>

    <!-- 状态图标 -->
    <x:String x:Key="InfoIcon">&#xE946;</x:String>
    <x:String x:Key="WarningIcon">&#xE7BA;</x:String>
    <x:String x:Key="ErrorIcon">&#xE783;</x:String>
    <x:String x:Key="SuccessIcon">&#xE73E;</x:String>
    <x:String x:Key="HelpIcon">&#xE897;</x:String>

    <!-- 搜索图标 -->
    <x:String x:Key="SearchIcon">&#xE721;</x:String>
    <x:String x:Key="FilterIcon">&#xE71C;</x:String>
    <x:String x:Key="SortIcon">&#xE8CB;</x:String>

    <!-- 视图图标 -->
    <x:String x:Key="ListIcon">&#xE8FD;</x:String>
    <x:String x:Key="GridIcon">&#xE80A;</x:String>
    <x:String x:Key="DetailsIcon">&#xE8BC;</x:String>

    <!-- 连接图标 -->
    <x:String x:Key="WifiIcon">&#xE701;</x:String>
    <x:String x:Key="BluetoothIcon">&#xE702;</x:String>
    <x:String x:Key="NetworkIcon">&#xE968;</x:String>

    <!-- 系统图标 -->
    <x:String x:Key="PowerIcon">&#xE7E8;</x:String>
    <x:String x:Key="BatteryIcon">&#xE83F;</x:String>
    <x:String x:Key="ClockIcon">&#xE823;</x:String>
    <x:String x:Key="CalendarIcon">&#xE787;</x:String>

    <!-- 图标样式定义 -->
    <Style x:Key="IconTextBlockStyle" TargetType="TextBlock">
        <Setter Property="FontFamily" Value="{StaticResource SymbolThemeFontFamily}"/>
        <Setter Property="FontSize" Value="16"/>
        <Setter Property="HorizontalAlignment" Value="Center"/>
        <Setter Property="VerticalAlignment" Value="Center"/>
        <Setter Property="TextAlignment" Value="Center"/>
    </Style>

    <Style x:Key="SmallIconStyle" TargetType="TextBlock" BasedOn="{StaticResource IconTextBlockStyle}">
        <Setter Property="FontSize" Value="12"/>
    </Style>

    <Style x:Key="MediumIconStyle" TargetType="TextBlock" BasedOn="{StaticResource IconTextBlockStyle}">
        <Setter Property="FontSize" Value="16"/>
    </Style>

    <Style x:Key="LargeIconStyle" TargetType="TextBlock" BasedOn="{StaticResource IconTextBlockStyle}">
        <Setter Property="FontSize" Value="20"/>
    </Style>

    <Style x:Key="ExtraLargeIconStyle" TargetType="TextBlock" BasedOn="{StaticResource IconTextBlockStyle}">
        <Setter Property="FontSize" Value="24"/>
    </Style>

    <!-- 图标按钮样式 -->
    <Style x:Key="IconButtonStyle" TargetType="Button">
        <Setter Property="Background" Value="Transparent"/>
        <Setter Property="BorderThickness" Value="0"/>
        <Setter Property="Padding" Value="8"/>
        <Setter Property="Width" Value="40"/>
        <Setter Property="Height" Value="40"/>
        <Setter Property="HorizontalContentAlignment" Value="Center"/>
        <Setter Property="VerticalContentAlignment" Value="Center"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="Button">
                    <Border Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            CornerRadius="4">
                        <ContentPresenter HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                                        VerticalAlignment="{TemplateBinding VerticalContentAlignment}"
                                        Margin="{TemplateBinding Padding}"/>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter Property="Background" Value="{DynamicResource ControlFillColorSecondaryBrush}"/>
                        </Trigger>
                        <Trigger Property="IsPressed" Value="True">
                            <Setter Property="Background" Value="{DynamicResource ControlFillColorTertiaryBrush}"/>
                        </Trigger>
                        <Trigger Property="IsEnabled" Value="False">
                            <Setter Property="Opacity" Value="0.5"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

</ResourceDictionary>
